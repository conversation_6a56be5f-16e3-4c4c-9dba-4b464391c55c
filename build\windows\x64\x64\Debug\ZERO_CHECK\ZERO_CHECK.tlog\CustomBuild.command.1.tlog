^C:\USERS\<USER>\DESKTOP\APPS\FLUTTER-APP\BUILD\WINDOWS\X64\CMAKEFILES\4D1C0D19DF22E0E1EC4E63AC382D6FA4\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/apps/flutter-app/windows -BC:/Users/<USER>/Desktop/apps/flutter-app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/apps/flutter-app/build/windows/x64/flutter_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
