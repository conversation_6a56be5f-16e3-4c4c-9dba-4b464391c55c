import 'package:flutter/material.dart';

class ApiConstants {
  // Update this URL to match your Laravel API
  static const String baseUrl = 'http://localhost/appnote-api/public/api';

  // Authentication endpoints
  static const String adminLogin = '$baseUrl/admin/login';
  static const String studentLogin = '$baseUrl/student/login';
  static const String employeeLogin = '$baseUrl/employee/login';

  // Other endpoints
  static const String adminProfile = '$baseUrl/admin/profile';
  static const String studentProfile = '$baseUrl/student/profile';
  static const String employeeProfile = '$baseUrl/employee/profile';
}

class StorageKeys {
  static const String userToken = 'user_token';
  static const String userType = 'user_type';
  static const String userData = 'user_data';
}

enum UserType { admin, student, employee }

// App Color Palette
class AppColors {
  // Primary colors from the palette
  static const Color parchment = Color(0xFFF4ECDC);
  static const Color costaDelSol = Color(0xFF5D6E35);
  static const Color locust = Color(0xFFABAE88);
  static const Color gurkha = Color(0xFF949C74);
  static const Color avocado = Color(0xFF949B6C);
  static const Color coralReef = Color(0xFFC4C2A4);
  static const Color gurkha2 = Color(0xFF9C9C74);
  static const Color thistleGreen = Color(0xFFD0CDB0);
  static const Color tana = Color(0xFFDED8BF);
  static const Color chino = Color(0xFFCCC4A9);

  // Role-specific colors
  static const Color adminPrimary = costaDelSol;
  static const Color studentPrimary = avocado;
  static const Color employeePrimary = gurkha;

  // UI colors
  static const Color background = parchment;
  static const Color surface = tana;
  static const Color cardBackground = thistleGreen;
  static const Color textPrimary = costaDelSol;
  static const Color textSecondary = gurkha;
  static const Color accent = locust;

  // Status colors
  static const Color success = Color(0xFF6B8E23);
  static const Color warning = Color(0xFFDAA520);
  static const Color error = Color(0xFFB22222);
}
