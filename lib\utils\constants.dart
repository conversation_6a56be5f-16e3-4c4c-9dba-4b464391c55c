class ApiConstants {
  // Update this URL to match your Laravel API
  static const String baseUrl = 'http://localhost/appnote-api/public/api';
  
  // Authentication endpoints
  static const String adminLogin = '$baseUrl/admin/login';
  static const String studentLogin = '$baseUrl/student/login';
  static const String employeeLogin = '$baseUrl/employee/login';
  
  // Other endpoints
  static const String adminProfile = '$baseUrl/admin/profile';
  static const String studentProfile = '$baseUrl/student/profile';
  static const String employeeProfile = '$baseUrl/employee/profile';
}

class StorageKeys {
  static const String userToken = 'user_token';
  static const String userType = 'user_type';
  static const String userData = 'user_data';
}

enum UserType {
  admin,
  student,
  employee,
}
