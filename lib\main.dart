import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/auth_service.dart';
import 'screens/login_selection_screen.dart';
import 'screens/dashboard_screen.dart';
import 'utils/constants.dart';
import 'utils/font_helper.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AuthService(),
      child: MaterialApp(
        title: 'معهد النبطية الفني',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppColors.logoGreen,
            brightness: Brightness.light,
            primary: AppColors.logoGreen,
            secondary: AppColors.logoMint,
            surface: AppColors.surface,
            onPrimary: Colors.white,
            onSecondary: AppColors.textPrimary,
            onSurface: AppColors.textPrimary,
            outline: AppColors.border,
          ),
          useMaterial3: true,
          scaffoldBackgroundColor: AppColors.background,
          appBarTheme: AppBarTheme(
            centerTitle: true,
            elevation: 0,
            backgroundColor: AppColors.logoGreen,
            foregroundColor: Colors.white,
            titleTextStyle: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              elevation: 2,
              backgroundColor: AppColors.logoGreen,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
          cardTheme: CardThemeData(
            elevation: 4,
            color: AppColors.cardBackground,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            filled: true,
            fillColor: AppColors.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.accent),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.accent),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.logoGreen, width: 2),
            ),
          ),
          fontFamily: FontHelper.defaultFontFamily,
          textTheme: const TextTheme(
            headlineLarge: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
            headlineMedium: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
            headlineSmall: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
            titleLarge: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
            titleMedium: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
              fontFamily: 'Cairo',
            ),
            titleSmall: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
              fontFamily: 'Cairo',
            ),
            bodyLarge: TextStyle(
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
            bodyMedium: TextStyle(
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            bodySmall: TextStyle(
              color: AppColors.textSecondary,
              fontFamily: 'Cairo',
            ),
            labelLarge: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
              fontFamily: 'Cairo',
            ),
            labelMedium: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
              fontFamily: 'Cairo',
            ),
            labelSmall: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        home: const AuthWrapper(),
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Initialize auth service when app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AuthService>(context, listen: false).init();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        // Show loading screen while checking authentication
        if (authService.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 20),
                  Text('Loading...', style: TextStyle(fontSize: 16)),
                ],
              ),
            ),
          );
        }

        // Navigate to appropriate screen based on authentication status
        if (authService.isAuthenticated) {
          return const DashboardScreen();
        } else {
          return const LoginSelectionScreen();
        }
      },
    );
  }
}
