^C:\USERS\<USER>\DESKTOP\APPS\FLUTTER-APP\BUILD\WINDOWS\X64\CMAKEFILES\1B002A5916B2AC1148ABAB5964A4FEBD\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\Desktop\apps\flutter-app FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\apps\flutter-app\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Desktop\apps\flutter-app FLUTTER_TARGET=C:\Users\<USER>\Desktop\apps\flutter-app\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049OGRlZmFhNzFhNw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTA5MTUwODkzOQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Desktop\apps\flutter-app\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\APPS\FLUTTER-APP\BUILD\WINDOWS\X64\CMAKEFILES\B813D49BCFDC38FB12BAB8C5942F5485\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\APPS\FLUTTER-APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/apps/flutter-app/windows -BC:/Users/<USER>/Desktop/apps/flutter-app/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/apps/flutter-app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
