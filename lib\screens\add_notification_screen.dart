import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/notification_service.dart';
import '../services/student_service.dart';
import '../services/employee_service.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';

class AddNotificationScreen extends StatefulWidget {
  const AddNotificationScreen({super.key});

  @override
  State<AddNotificationScreen> createState() => _AddNotificationScreenState();
}

class _AddNotificationScreenState extends State<AddNotificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _scheduledAtController = TextEditingController();
  final _expiresAtController = TextEditingController();
  final _attachmentNameController = TextEditingController();
  final _attachmentPathController = TextEditingController();
  final _attachmentSizeController = TextEditingController();

  String _selectedPriority = 'medium';
  String _selectedTargetAudience = 'all';
  String? _selectedLevel;
  String? _selectedClass;
  String? _selectedContractType;
  String? _selectedJobStatus;
  bool _isActive = true;
  bool _isLoading = false;

  // Lists for filtering options
  List<String> _availableLevels = [];
  List<String> _availableClasses = [];
  List<String> _availableContractTypes = [];
  List<String> _availableJobStatuses = [];

  // Multiple attachments variables
  List<File> _selectedFiles = [];
  List<String> _selectedFileNames = [];

  // القوائم المنسدلة
  final List<Map<String, String>> _priorities = [
    {'value': 'low', 'label': 'منخفضة'},
    {'value': 'medium', 'label': 'متوسطة'},
    {'value': 'high', 'label': 'عالية'},
  ];

  final List<Map<String, String>> _targetAudiences = [
    {'value': 'all', 'label': 'جميع المستخدمين (طلاب + موظفين)'},
    {'value': 'students', 'label': 'جميع الطلاب'},
    {'value': 'students_by_level', 'label': 'طلاب حسب المستوى'},
    {'value': 'students_by_class', 'label': 'طلاب حسب الصف'},
    {'value': 'employees', 'label': 'جميع الموظفين'},
    {'value': 'employees_by_contract', 'label': 'موظفين حسب نوع العقد'},
    {'value': 'employees_by_status', 'label': 'موظفين حسب حالة الوظيفة'},
  ];

  @override
  void initState() {
    super.initState();
    _loadFilterOptions();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _scheduledAtController.dispose();
    _expiresAtController.dispose();
    _attachmentNameController.dispose();
    _attachmentPathController.dispose();
    _attachmentSizeController.dispose();
    super.dispose();
  }

  Future<void> _loadFilterOptions() async {
    try {
      // Load student options
      final studentFormData = await StudentService.getFormData();
      _availableLevels = studentFormData['levels'] as List<String>;
      _availableClasses = studentFormData['classes'] as List<String>;

      // Load employee options
      final employeeFormData = await EmployeeService.getEmployeeOptions();
      _availableContractTypes =
          employeeFormData['contractTypes'] as List<String>;
      _availableJobStatuses = employeeFormData['jobStatuses'] as List<String>;

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading filter options: $e');
      }
    }
  }

  Future<void> _saveNotification() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final notificationData = <String, dynamic>{
        'title': _titleController.text.trim(),
        'message': _contentController.text
            .trim(), // Laravel API expects 'message' not 'content'
        'priority': _selectedPriority,
        'target_audience': _selectedTargetAudience,
        'is_active': _isActive,
        'sender_type': 'admin',
        'sender_id': 1, // Default admin ID
        'sender_name': 'admin', // Default admin name
        'recipient_type': _selectedTargetAudience,
      };

      // إضافة recipient_ids حسب نوع الجمهور المستهدف (كـ JSON string)
      switch (_selectedTargetAudience) {
        case 'all':
          notificationData['recipient_ids'] = json.encode(['all']);
          break;
        case 'students':
          notificationData['recipient_ids'] = json.encode(['students']);
          break;
        case 'students_by_level':
          if (_selectedLevel != null) {
            notificationData['recipient_ids'] = json.encode(['students']);
            notificationData['filter_type'] = 'level';
            notificationData['filter_value'] = _selectedLevel;
          } else {
            notificationData['recipient_ids'] = json.encode(['students']);
          }
          break;
        case 'students_by_class':
          if (_selectedClass != null) {
            notificationData['recipient_ids'] = json.encode(['students']);
            notificationData['filter_type'] = 'class';
            notificationData['filter_value'] = _selectedClass;
          } else {
            notificationData['recipient_ids'] = json.encode(['students']);
          }
          break;
        case 'employees':
          notificationData['recipient_ids'] = json.encode(['employees']);
          break;
        case 'employees_by_contract':
          if (_selectedContractType != null) {
            notificationData['recipient_ids'] = json.encode(['employees']);
            notificationData['filter_type'] = 'contract_type';
            notificationData['filter_value'] = _selectedContractType;
          } else {
            notificationData['recipient_ids'] = json.encode(['employees']);
          }
          break;
        case 'employees_by_status':
          if (_selectedJobStatus != null) {
            notificationData['recipient_ids'] = json.encode(['employees']);
            notificationData['filter_type'] = 'job_status';
            notificationData['filter_value'] = _selectedJobStatus;
          } else {
            notificationData['recipient_ids'] = json.encode(['employees']);
          }
          break;
        default:
          notificationData['recipient_ids'] = json.encode(['all']);
      }

      // إضافة الحقول الاختيارية
      if (_scheduledAtController.text.isNotEmpty) {
        notificationData['scheduled_at'] = _scheduledAtController.text;
      }

      if (_expiresAtController.text.isNotEmpty) {
        notificationData['expires_at'] = _expiresAtController.text;
      }

      // إضافة معلومات المرفقات المتعددة إذا تم اختيارها
      if (_selectedFileNames.isNotEmpty) {
        notificationData['attachment_names'] = json.encode(_selectedFileNames);
        notificationData['attachment_count'] = _selectedFileNames.length;
        notificationData['has_attachments'] = true;

        // For Laravel API compatibility - send first file info
        notificationData['attachment_name'] = _selectedFileNames.first;
      } else {
        notificationData['has_attachments'] = false;
      }

      if (kDebugMode) {
        print('📤 Creating notification: $notificationData');
        print('🔍 Attachment names: $_selectedFileNames');
        print('🔍 Selected files count: ${_selectedFiles.length}');
      }

      if (kDebugMode) {
        print(
          '🔍 STEP 1: About to call NotificationService.createNotification',
        );
        print('🔍 STEP 2: Data to be sent: $notificationData');
      }

      try {
        if (kDebugMode) {
          print('🔍 STEP 3: Calling NotificationService.createNotification...');
        }

        await NotificationService.createNotification(notificationData);

        if (kDebugMode) {
          print(
            '✅ STEP 4: Notification created successfully - NO ERROR IN FLUTTER',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ STEP 4: ERROR CAUGHT IN FLUTTER APP: $e');
          print('📋 Error type: ${e.runtimeType}');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إنشاء الإشعار: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return; // Don't rethrow, just show error and return
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم إنشاء الإشعار بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true); // إرجاع true للإشارة إلى النجاح
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: true, // Allow multiple files
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          for (var file in result.files) {
            if (file.path != null) {
              _selectedFiles.add(File(file.path!));
              _selectedFileNames.add(file.name);
            }
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking files: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الملفات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
      _selectedFileNames.removeAt(index);
    });
  }

  void _removeAllFiles() {
    setState(() {
      _selectedFiles.clear();
      _selectedFileNames.clear();
    });
  }

  String _getFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      case 'ppt':
      case 'pptx':
        return 'application/vnd.ms-powerpoint';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'txt':
        return 'text/plain';
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/x-rar-compressed';
      default:
        return 'application/octet-stream';
    }
  }

  String _getFileSizeString(File file) {
    try {
      final bytes = file.lengthSync();
      if (bytes < 1024) {
        return '$bytes B';
      } else if (bytes < 1024 * 1024) {
        return '${(bytes / 1024).toStringAsFixed(1)} KB';
      } else {
        return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      }
    } catch (e) {
      return 'غير محدد';
    }
  }

  Widget _buildSubFilterDropdown() {
    switch (_selectedTargetAudience) {
      case 'students_by_level':
        return Column(
          children: [
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedLevel,
              decoration: const InputDecoration(
                labelText: 'اختر المستوى *',
                prefixIcon: Icon(Icons.school),
                border: OutlineInputBorder(),
              ),
              items: _availableLevels.map((level) {
                return DropdownMenuItem<String>(
                  value: level,
                  child: Text(level),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLevel = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار المستوى';
                }
                return null;
              },
            ),
          ],
        );
      case 'students_by_class':
        return Column(
          children: [
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedClass,
              decoration: const InputDecoration(
                labelText: 'اختر الصف *',
                prefixIcon: Icon(Icons.class_),
                border: OutlineInputBorder(),
              ),
              items: _availableClasses.map((className) {
                return DropdownMenuItem<String>(
                  value: className,
                  child: Text(className),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedClass = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار الصف';
                }
                return null;
              },
            ),
          ],
        );
      case 'employees_by_contract':
        return Column(
          children: [
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedContractType,
              decoration: const InputDecoration(
                labelText: 'اختر نوع العقد *',
                prefixIcon: Icon(Icons.work),
                border: OutlineInputBorder(),
              ),
              items: _availableContractTypes.map((contractType) {
                return DropdownMenuItem<String>(
                  value: contractType,
                  child: Text(contractType),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedContractType = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار نوع العقد';
                }
                return null;
              },
            ),
          ],
        );
      case 'employees_by_status':
        return Column(
          children: [
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedJobStatus,
              decoration: const InputDecoration(
                labelText: 'اختر حالة الوظيفة *',
                prefixIcon: Icon(Icons.work_outline),
                border: OutlineInputBorder(),
              ),
              items: _availableJobStatuses.map((jobStatus) {
                return DropdownMenuItem<String>(
                  value: jobStatus,
                  child: Text(jobStatus),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedJobStatus = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار حالة الوظيفة';
                }
                return null;
              },
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildAttachmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'المرفقات (اختياري)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF5D6E35),
              ),
            ),
            const Spacer(),
            if (_selectedFiles.isNotEmpty) ...[
              Text(
                '${_selectedFiles.length} ملف محدد',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: _removeAllFiles,
                child: const Text(
                  'إزالة الكل',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Add files button
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey.shade50,
          ),
          child: Column(
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 48,
                color: Colors.grey.shade600,
              ),
              const SizedBox(height: 8),
              Text(
                _selectedFiles.isEmpty
                    ? 'اختر ملفات للإرفاق'
                    : 'إضافة ملفات أخرى',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _pickFiles,
                icon: const Icon(Icons.attach_file),
                label: Text(
                  _selectedFiles.isEmpty ? 'اختيار ملفات' : 'إضافة ملفات',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF5D6E35),
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),

        // Display selected files
        if (_selectedFiles.isNotEmpty) ...[
          const SizedBox(height: 12),
          ...List.generate(_selectedFiles.length, (index) {
            final file = _selectedFiles[index];
            final fileName = _selectedFileNames[index];
            return Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.green.shade50,
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.insert_drive_file,
                    color: Colors.green.shade700,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          fileName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 13,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'حجم الملف: ${_getFileSizeString(file)}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _removeFile(index),
                    icon: Icon(
                      Icons.close,
                      color: Colors.red.shade600,
                      size: 20,
                    ),
                    tooltip: 'إزالة الملف',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4ECDC),
      appBar: AppBar(
        title: const Text(
          'إضافة إشعار جديد',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF5D6E35),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              CustomTextField(
                controller: _titleController,
                labelText: 'عنوان الإشعار *',
                hintText: 'أدخل عنوان الإشعار',
                prefixIcon: Icons.title,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'عنوان الإشعار مطلوب';
                  }
                  if (value.trim().length > 255) {
                    return 'عنوان الإشعار يجب أن يكون أقل من 255 حرف';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // المحتوى
              CustomTextField(
                controller: _contentController,
                labelText: 'محتوى الإشعار *',
                hintText: 'أدخل محتوى الإشعار',
                prefixIcon: Icons.description,
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'محتوى الإشعار مطلوب';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // أولوية الإشعار
              DropdownButtonFormField<String>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'أولوية الإشعار *',
                  prefixIcon: Icon(Icons.priority_high),
                  border: OutlineInputBorder(),
                ),
                items: _priorities.map((priority) {
                  return DropdownMenuItem<String>(
                    value: priority['value'],
                    child: Text(priority['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPriority = value!;
                  });
                },
              ),

              const SizedBox(height: 16),

              // الجمهور المستهدف
              DropdownButtonFormField<String>(
                value: _selectedTargetAudience,
                decoration: const InputDecoration(
                  labelText: 'الجمهور المستهدف *',
                  prefixIcon: Icon(Icons.group),
                  border: OutlineInputBorder(),
                ),
                items: _targetAudiences.map((audience) {
                  return DropdownMenuItem<String>(
                    value: audience['value'],
                    child: Text(audience['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedTargetAudience = value!;
                    // Reset sub-filter selections when target audience changes
                    _selectedLevel = null;
                    _selectedClass = null;
                    _selectedContractType = null;
                    _selectedJobStatus = null;
                  });
                },
              ),

              // Sub-filter dropdown (appears based on selected target audience)
              _buildSubFilterDropdown(),

              const SizedBox(height: 16),

              // قسم المرفقات
              _buildAttachmentSection(),

              const SizedBox(height: 16),

              // حالة النشاط
              SwitchListTile(
                title: const Text('الإشعار نشط'),
                subtitle: const Text('تفعيل أو إلغاء تفعيل الإشعار'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                activeColor: Colors.green,
              ),

              const SizedBox(height: 24),

              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'حفظ الإشعار',
                      onPressed: _isLoading ? null : _saveNotification,
                      backgroundColor: Colors.green,
                      isLoading: _isLoading,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomButton(
                      text: 'إلغاء',
                      onPressed: _isLoading
                          ? null
                          : () => Navigator.of(context).pop(),
                      backgroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
