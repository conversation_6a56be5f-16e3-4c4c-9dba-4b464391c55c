import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/notification.dart';
import '../services/notification_service.dart';
import '../utils/constants.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';
import 'add_notification_screen.dart';

class ManageNotificationsScreen extends StatefulWidget {
  const ManageNotificationsScreen({super.key});

  @override
  State<ManageNotificationsScreen> createState() =>
      _ManageNotificationsScreenState();
}

class _ManageNotificationsScreenState extends State<ManageNotificationsScreen> {
  List<AppNotification> _notifications = [];
  List<AppNotification> _filteredNotifications = [];
  bool _isLoading = true;

  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('📋 Loading notifications...');
      }

      final notifications = await NotificationService.getNotifications();

      setState(() {
        _notifications = notifications;
        _filteredNotifications = notifications;
        _isLoading = false;
      });

      if (kDebugMode) {
        print('✅ Loaded ${notifications.length} notifications');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading notifications: $e');
      }

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        String errorMessage = 'خطأ في تحميل الإشعارات';

        if (e.toString().contains('No authentication token')) {
          errorMessage = 'يجب تسجيل الدخول أولاً';
        } else if (e.toString().contains('Authentication failed')) {
          errorMessage =
              'انتهت صلاحية تسجيل الدخول. يرجى تسجيل الدخول مرة أخرى';
        } else if (e.toString().contains('Cannot connect to server')) {
          errorMessage = 'لا يمكن الاتصال بالخادم. تأكد من تشغيل Laravel API';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
            action:
                e.toString().contains('authentication') ||
                    e.toString().contains('token')
                ? SnackBarAction(
                    label: 'تسجيل الدخول',
                    textColor: Colors.white,
                    onPressed: () {
                      Navigator.of(context).pushReplacementNamed('/login');
                    },
                  )
                : null,
          ),
        );
      }
    }
  }

  void _filterNotifications(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredNotifications = _notifications;
      } else {
        _filteredNotifications = _notifications.where((notification) {
          return notification.title.toLowerCase().contains(
                query.toLowerCase(),
              ) ||
              notification.content.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void _deleteNotification(AppNotification notification) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف', style: TextStyle(fontFamily: 'Cairo')),
        content: Text(
          'هل أنت متأكد من حذف الإشعار "${notification.title}"؟',
          style: const TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('حذف', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await NotificationService.deleteNotification(notification.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف الإشعار "${notification.title}" بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }

        _loadNotifications(); // Refresh the list
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الإشعار: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  void _sendNotification(AppNotification notification) async {
    try {
      await NotificationService.sendNotification(notification.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إرسال الإشعار "${notification.title}" بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }

      _loadNotifications(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الإشعار: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة الإشعارات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5D6E35), // Costa del sol
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotifications,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.adminPrimary.withValues(alpha: 0.1),
              AppColors.background,
            ],
          ),
        ),
        child: Column(
          children: [
            // Header with actions
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Action buttons row
                  Row(
                    children: [
                      // Add new notification button
                      Expanded(
                        child: CustomButton(
                          text: 'إضافة إشعار جديد',
                          onPressed: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const AddNotificationScreen(),
                              ),
                            );

                            if (result == true) {
                              _loadNotifications(); // Refresh the list
                            }
                          },
                          backgroundColor: AppColors.success,
                          icon: Icons.add,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Search field
                  CustomTextField(
                    controller: _searchController,
                    labelText: 'البحث عن إشعار',
                    hintText: 'ابحث بالعنوان أو المحتوى',
                    prefixIcon: Icons.search,
                    onChanged: _filterNotifications,
                  ),
                ],
              ),
            ),

            // Notifications count and info
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Icon(Icons.notifications, color: AppColors.textSecondary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'إجمالي الإشعارات: ${_notifications.length}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Notifications list
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Color(0xFF5D6E35)),
                          SizedBox(height: 20),
                          Text(
                            'جاري تحميل الإشعارات...',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 16,
                              color: Color(0xFF5D6E35),
                            ),
                          ),
                        ],
                      ),
                    )
                  : _filteredNotifications.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.notifications_none,
                            size: 80,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 20),
                          Text(
                            'لا توجد إشعارات',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(20),
                      itemCount: _filteredNotifications.length,
                      itemBuilder: (context, index) {
                        final notification = _filteredNotifications[index];
                        return _buildNotificationCard(notification);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCard(AppNotification notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.costaDelSol.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: _getTypeColor(
            notification.type,
          ).withValues(alpha: 0.1),
          child: Icon(
            _getTypeIcon(notification.type),
            color: _getTypeColor(notification.type),
          ),
        ),
        title: Text(
          notification.title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.content.length > 80
                  ? '${notification.content.substring(0, 80)}...'
                  : notification.content,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  notification.isActive ? Icons.check_circle : Icons.cancel,
                  size: 16,
                  color: notification.isActive ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 4),
                Text(
                  notification.isActive ? 'نشط' : 'غير نشط',
                  style: TextStyle(
                    fontSize: 12,
                    color: notification.isActive ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'send':
                _sendNotification(notification);
                break;
              case 'edit':
                // TODO: Navigate to edit notification screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('سيتم فتح شاشة تعديل الإشعار قريباً'),
                  ),
                );
                break;
              case 'delete':
                _deleteNotification(notification);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'send',
              child: Row(
                children: [
                  Icon(Icons.send, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Text('إرسال'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'info':
        return Icons.info;
      case 'warning':
        return Icons.warning;
      case 'success':
        return Icons.check_circle;
      case 'error':
        return Icons.error;
      default:
        return Icons.notifications;
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'info':
        return Colors.blue;
      case 'warning':
        return Colors.orange;
      case 'success':
        return Colors.green;
      case 'error':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
