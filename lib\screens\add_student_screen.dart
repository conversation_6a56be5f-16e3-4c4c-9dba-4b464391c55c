import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/student_service.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_dropdown.dart';

class AddStudentScreen extends StatefulWidget {
  const AddStudentScreen({super.key});

  @override
  State<AddStudentScreen> createState() => _AddStudentScreenState();
}

class _AddStudentScreenState extends State<AddStudentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  String? _selectedSpecialization;
  String? _selectedSection;
  String? _selectedClass;
  String? _selectedLevel;
  String? _selectedResult;
  String? _selectedNationality;

  bool _isLoading = false;
  bool _isLoadingData = true;

  // Available options from database
  List<String> _specializations = [];
  List<String> _sections = [];
  List<String> _classes = [];
  List<String> _levels = [];
  List<String> _results = [];
  List<String> _nationalities = [];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    try {
      setState(() {
        _isLoadingData = true;
      });

      if (kDebugMode) {
        print('🚀 Loading form data...');
      }

      // Load all data in one optimized call
      final formData = await StudentService.getFormData();

      // Set values from the single API call
      _usernameController.text = formData['username'] as String;
      _specializations = formData['specializations'] as List<String>;
      _sections = formData['sections'] as List<String>;
      _classes = formData['classes'] as List<String>;
      _levels = formData['levels'] as List<String>;
      _results = formData['results'] as List<String>;
      _nationalities = formData['nationalities'] as List<String>;

      _selectedNationality = _nationalities.isNotEmpty
          ? _nationalities.first
          : 'لبناني';
      _passwordController.text = 'stu123';

      if (kDebugMode) {
        print('✅ Form data loaded successfully:');
        print('   Username: ${_usernameController.text}');
        print('   Specializations: ${_specializations.length}');
        print('   Sections: ${_sections.length}');
        print('   Classes: ${_classes.length}');
        print('   Levels: ${_levels.length}');
        print('   Results: ${_results.length}');
        print('   Nationalities: ${_nationalities.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading initial data: $e');
      }
      // Set fallback values
      _usernameController.text =
          'stu${DateTime.now().millisecondsSinceEpoch.toString().substring(9)}';
      _selectedNationality = 'لبناني';
      _nationalities = ['لبناني', 'سوري', 'فلسطيني', 'مصري', 'أردني'];
      _passwordController.text = 'stu123';

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تعذر تحميل البيانات، سيتم استخدام القيم الافتراضية'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingData = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _saveStudent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedSpecialization == null ||
        _selectedSection == null ||
        _selectedClass == null ||
        _selectedLevel == null ||
        _selectedNationality == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة (النتيجة اختيارية)'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إرسال البيانات بالتنسيق الصحيح حسب Laravel API
      final studentData = {
        'username': _usernameController.text.trim(), // مطلوب
        'full_name': _fullNameController.text.trim(), // مطلوب
        'phone': _phoneController.text.trim(), // اختياري
        'class': _selectedClass!, // اختياري
        'nationality': _selectedNationality ?? 'اللبنانية', // اختياري
        'specialization': _selectedSpecialization!, // اختياري
        'section': _selectedSection!, // اختياري
        'level': _selectedLevel!, // اختياري
        'result': _selectedResult?.isEmpty == true
            ? null
            : _selectedResult, // اختياري
        'password': _passwordController.text.trim().isEmpty
            ? 'stu123'
            : _passwordController.text.trim(), // اختياري - افتراضي stu123
        'is_active': 1,
      };

      if (kDebugMode) {
        print('Creating student with data: $studentData');
      }

      final newStudent = await StudentService.createStudent(studentData);

      if (newStudent != null) {
        // Clear cache to ensure fresh data next time
        StudentService.clearCache();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة الطالب بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إضافة الطالب'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating student: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4ECDC), // Parchment background
      appBar: AppBar(
        title: const Text(
          'إضافة طالب جديد',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5D6E35), // Costa del sol
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoadingData
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Color(0xFF5D6E35)),
                  SizedBox(height: 20),
                  Text(
                    'جاري تحميل البيانات...',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                      color: Color(0xFF5D6E35),
                    ),
                  ),
                ],
              ),
            )
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Column(
                        children: [
                          Icon(
                            Icons.person_add,
                            size: 50,
                            color: Color(0xFF5D6E35),
                          ),
                          SizedBox(height: 10),
                          Text(
                            'إضافة طالب جديد',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF5D6E35),
                            ),
                          ),
                          Text(
                            'املأ البيانات التالية لإضافة طالب جديد',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Form Fields
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Username (Auto-generated)
                          CustomTextField(
                            controller: _usernameController,
                            labelText: 'اسم المستخدم (تلقائي)',
                            hintText: 'سيتم توليده تلقائياً',
                            prefixIcon: Icons.account_circle,
                            enabled: false, // Read-only
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'خطأ في توليد اسم المستخدم';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 20),

                          // Full Name
                          CustomTextField(
                            controller: _fullNameController,
                            labelText: 'الاسم الكامل',
                            hintText: 'أدخل الاسم الكامل',
                            prefixIcon: Icons.person,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال الاسم الكامل';
                              }
                              if (value.length < 2) {
                                return 'الاسم يجب أن يكون حرفين على الأقل';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 20),

                          // Phone
                          CustomTextField(
                            controller: _phoneController,
                            labelText: 'رقم الهاتف',
                            hintText: '+961-XX-XXXXXX',
                            prefixIcon: Icons.phone,
                            keyboardType: TextInputType.phone,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال رقم الهاتف';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 20),

                          // Nationality
                          CustomDropdown<String>(
                            value: _selectedNationality,
                            items: _nationalities,
                            labelText: 'الجنسية',
                            hintText: 'اختر الجنسية',
                            prefixIcon: Icons.flag,
                            onChanged: (value) {
                              setState(() {
                                _selectedNationality = value;
                              });
                            },
                          ),

                          const SizedBox(height: 20),

                          // Specialization
                          CustomDropdown<String>(
                            value: _selectedSpecialization,
                            items: _specializations,
                            labelText: 'التخصص',
                            hintText: 'اختر التخصص',
                            prefixIcon: Icons.school,
                            onChanged: (value) {
                              setState(() {
                                _selectedSpecialization = value;
                              });
                            },
                          ),

                          const SizedBox(height: 20),

                          // Class and Section Row
                          Row(
                            children: [
                              Expanded(
                                child: CustomDropdown<String>(
                                  value: _selectedClass,
                                  items: _classes,
                                  labelText: 'الصف',
                                  hintText: 'اختر الصف',
                                  prefixIcon: Icons.class_,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedClass = value;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 15),
                              Expanded(
                                child: CustomDropdown<String>(
                                  value: _selectedSection,
                                  items: _sections,
                                  labelText: 'الشعبة',
                                  hintText: 'اختر الشعبة',
                                  prefixIcon: Icons.group,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedSection = value;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 20),

                          // Level and Result Row
                          Row(
                            children: [
                              Expanded(
                                child: CustomDropdown<String>(
                                  value: _selectedLevel,
                                  items: _levels,
                                  labelText: 'المستوى',
                                  hintText: 'اختر المستوى',
                                  prefixIcon: Icons.star,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedLevel = value;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 15),
                              Expanded(
                                child: CustomDropdown<String>(
                                  value: _selectedResult,
                                  items: ['', ..._results], // Add empty option
                                  labelText: 'النتيجة (اختياري)',
                                  hintText: 'اختر النتيجة أو اتركها فارغة',
                                  prefixIcon: Icons.assignment_turned_in,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedResult = value?.isEmpty == true
                                          ? null
                                          : value;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 20),

                          // Password
                          CustomTextField(
                            controller: _passwordController,
                            labelText: 'كلمة المرور',
                            hintText: 'أدخل كلمة المرور',
                            prefixIcon: Icons.lock,
                            obscureText: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال كلمة المرور';
                              }
                              if (value.length < 3) {
                                return 'كلمة المرور يجب أن تكون 3 أحرف على الأقل';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Save Button
                    CustomButton(
                      text: _isLoading ? 'جاري الحفظ...' : 'حفظ الطالب',
                      onPressed: _isLoading ? null : _saveStudent,
                      backgroundColor: const Color(0xFF5D6E35),
                      textColor: Colors.white,
                      icon: _isLoading ? null : Icons.save,
                    ),

                    const SizedBox(height: 20),

                    // Cancel Button
                    CustomButton(
                      text: 'إلغاء',
                      onPressed: _isLoading
                          ? null
                          : () {
                              Navigator.of(context).pop();
                            },
                      backgroundColor: Colors.grey,
                      textColor: Colors.white,
                      icon: Icons.cancel,
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
