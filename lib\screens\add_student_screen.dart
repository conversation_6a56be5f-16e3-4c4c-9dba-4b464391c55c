import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/student_service.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_dropdown.dart';

class AddStudentScreen extends StatefulWidget {
  const AddStudentScreen({super.key});

  @override
  State<AddStudentScreen> createState() => _AddStudentScreenState();
}

class _AddStudentScreenState extends State<AddStudentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _nationalityController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  String? _selectedSpecialization;
  String? _selectedSection;
  String? _selectedClass;
  String? _selectedLevel;
  String? _selectedResult;

  bool _isLoading = false;

  // Available options
  final List<String> _specializations = [
    'المعلوماتية الإدارية',
    'العناية التمريضية',
    'تكنولوجيا المعلوماتية',
    'الكهرباء',
    'ميكانيك السيارات',
    'البناء والأشغال العامة',
    'الفنون الزخرفية',
    'الرقابة الصحية',
    'التبريد والتكييف',
    'اللحام والتشكيل',
  ];

  final List<String> _sections = ['أ', 'ب', 'ج'];

  final List<String> _classes = [
    'TS1', 'TS2', // الامتياز الفني
    'BT1', 'BT2', 'BT3', // البكالوريا الفنية
    'LT', // الاجازة الفنية
    'BP1', 'BP2', // التكميلية المهنية
  ];

  final List<String> _levels = ['ممتاز', 'جيد جداً', 'جيد', 'مقبول'];
  final List<String> _results = ['نجح', 'راسب', 'منقول', 'مكمل'];

  @override
  void initState() {
    super.initState();
    // Set default values
    _nationalityController.text = 'لبناني';
    _passwordController.text = 'stu123';
    _selectedResult = 'نجح';
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _nationalityController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _saveStudent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedSpecialization == null ||
        _selectedSection == null ||
        _selectedClass == null ||
        _selectedLevel == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final studentData = {
        'username': _usernameController.text.trim(),
        'full_name': _fullNameController.text.trim(),
        'nationality': _nationalityController.text.trim(),
        'phone': _phoneController.text.trim(),
        'specialization': _selectedSpecialization!,
        'section': _selectedSection!,
        'class': _selectedClass!,
        'level': _selectedLevel!,
        'result': _selectedResult!,
        'password': _passwordController.text.trim(),
        'is_active': true,
      };

      if (kDebugMode) {
        print('Creating student with data: $studentData');
      }

      final newStudent = await StudentService.createStudent(studentData);

      if (newStudent != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة الطالب بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إضافة الطالب'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating student: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4ECDC), // Parchment background
      appBar: AppBar(
        title: const Text(
          'إضافة طالب جديد',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5D6E35), // Costa del sol
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Column(
                  children: [
                    Icon(
                      Icons.person_add,
                      size: 50,
                      color: Color(0xFF5D6E35),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'إضافة طالب جديد',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF5D6E35),
                      ),
                    ),
                    Text(
                      'املأ البيانات التالية لإضافة طالب جديد',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Form Fields
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Username
                    CustomTextField(
                      controller: _usernameController,
                      labelText: 'اسم المستخدم',
                      hintText: 'أدخل اسم المستخدم',
                      prefixIcon: Icons.account_circle,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم المستخدم';
                        }
                        if (value.length < 3) {
                          return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // Full Name
                    CustomTextField(
                      controller: _fullNameController,
                      labelText: 'الاسم الكامل',
                      hintText: 'أدخل الاسم الكامل',
                      prefixIcon: Icons.person,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الاسم الكامل';
                        }
                        if (value.length < 2) {
                          return 'الاسم يجب أن يكون حرفين على الأقل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // Phone
                    CustomTextField(
                      controller: _phoneController,
                      labelText: 'رقم الهاتف',
                      hintText: '+961-XX-XXXXXX',
                      prefixIcon: Icons.phone,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال رقم الهاتف';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // Nationality
                    CustomTextField(
                      controller: _nationalityController,
                      labelText: 'الجنسية',
                      hintText: 'أدخل الجنسية',
                      prefixIcon: Icons.flag,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الجنسية';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // Specialization
                    CustomDropdown<String>(
                      value: _selectedSpecialization,
                      items: _specializations,
                      labelText: 'التخصص',
                      hintText: 'اختر التخصص',
                      prefixIcon: Icons.school,
                      onChanged: (value) {
                        setState(() {
                          _selectedSpecialization = value;
                        });
                      },
                    ),

                    const SizedBox(height: 20),

                    // Class and Section Row
                    Row(
                      children: [
                        Expanded(
                          child: CustomDropdown<String>(
                            value: _selectedClass,
                            items: _classes,
                            labelText: 'الصف',
                            hintText: 'اختر الصف',
                            prefixIcon: Icons.class_,
                            onChanged: (value) {
                              setState(() {
                                _selectedClass = value;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: CustomDropdown<String>(
                            value: _selectedSection,
                            items: _sections,
                            labelText: 'الشعبة',
                            hintText: 'اختر الشعبة',
                            prefixIcon: Icons.group,
                            onChanged: (value) {
                              setState(() {
                                _selectedSection = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Level and Result Row
                    Row(
                      children: [
                        Expanded(
                          child: CustomDropdown<String>(
                            value: _selectedLevel,
                            items: _levels,
                            labelText: 'المستوى',
                            hintText: 'اختر المستوى',
                            prefixIcon: Icons.star,
                            onChanged: (value) {
                              setState(() {
                                _selectedLevel = value;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: CustomDropdown<String>(
                            value: _selectedResult,
                            items: _results,
                            labelText: 'النتيجة',
                            hintText: 'اختر النتيجة',
                            prefixIcon: Icons.assignment_turned_in,
                            onChanged: (value) {
                              setState(() {
                                _selectedResult = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Password
                    CustomTextField(
                      controller: _passwordController,
                      labelText: 'كلمة المرور',
                      hintText: 'أدخل كلمة المرور',
                      prefixIcon: Icons.lock,
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال كلمة المرور';
                        }
                        if (value.length < 3) {
                          return 'كلمة المرور يجب أن تكون 3 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Save Button
              CustomButton(
                text: _isLoading ? 'جاري الحفظ...' : 'حفظ الطالب',
                onPressed: _isLoading ? null : _saveStudent,
                backgroundColor: const Color(0xFF5D6E35),
                textColor: Colors.white,
                icon: _isLoading ? null : Icons.save,
              ),

              const SizedBox(height: 20),

              // Cancel Button
              CustomButton(
                text: 'إلغاء',
                onPressed: _isLoading
                    ? null
                    : () {
                        Navigator.of(context).pop();
                      },
                backgroundColor: Colors.grey,
                textColor: Colors.white,
                icon: Icons.cancel,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
