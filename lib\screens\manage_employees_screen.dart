import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/employee.dart';
import '../services/employee_service.dart';
import '../utils/constants.dart';
import '../widgets/custom_button.dart';
import 'add_employee_screen.dart';
import 'edit_employee_screen.dart';
import '../widgets/custom_text_field.dart';

class ManageEmployeesScreen extends StatefulWidget {
  const ManageEmployeesScreen({super.key});

  @override
  State<ManageEmployeesScreen> createState() => _ManageEmployeesScreenState();
}

class _ManageEmployeesScreenState extends State<ManageEmployeesScreen> {
  List<Employee> _employees = [];
  List<Employee> _filteredEmployees = [];
  bool _isLoading = true;
  bool _isSearching = false;

  final _searchController = TextEditingController();

  // Pagination variables
  int _currentPage = 1;
  final int _itemsPerPage = 20;
  int _totalPages = 1;
  int _totalEmployees = 0;
  List<Employee> _currentPageEmployees = [];

  // Search debounce
  Timer? _searchDebounce;
  String _currentSearch = '';

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('📋 Loading employees...');
      }

      // Load all employees from API (always refresh to get latest data)
      final employees = await EmployeeService.getEmployees();
      _employees = employees;

      // Apply search filter locally
      _filteredEmployees = _employees.where((employee) {
        if (_currentSearch.isEmpty) return true;

        final searchLower = _currentSearch.toLowerCase();
        return employee.fullName.toLowerCase().contains(searchLower) ||
            (employee.phone?.toLowerCase().contains(searchLower) ?? false) ||
            (employee.contractType?.toLowerCase().contains(searchLower) ??
                false) ||
            (employee.employeeType?.toLowerCase().contains(searchLower) ??
                false);
      }).toList();

      // Calculate pagination
      _totalEmployees = _filteredEmployees.length;
      _totalPages = (_totalEmployees / _itemsPerPage).ceil();
      if (_totalPages == 0) _totalPages = 1;

      // Reset to first page if current page is beyond total pages
      if (_currentPage > _totalPages) {
        _currentPage = 1;
      }

      // Get current page data
      _updateCurrentPageEmployees();

      setState(() {
        _isLoading = false;
      });

      if (kDebugMode) {
        print(
          '✅ Loaded ${_totalEmployees} employees (filtered from ${_employees.length})',
        );
        print('📄 Page $_currentPage of $_totalPages');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading employees: $e');
      }

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الموظفين: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _updateCurrentPageEmployees() {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;

    _currentPageEmployees = _filteredEmployees.sublist(
      startIndex,
      endIndex > _filteredEmployees.length
          ? _filteredEmployees.length
          : endIndex,
    );
  }

  void _goToPage(int page) {
    if (page >= 1 && page <= _totalPages && page != _currentPage) {
      setState(() {
        _currentPage = page;
        _updateCurrentPageEmployees();
      });
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _goToPage(_currentPage + 1);
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _goToPage(_currentPage - 1);
    }
  }

  void _onSearchChanged(String value) {
    // Cancel previous timer
    _searchDebounce?.cancel();

    // Set new timer
    _searchDebounce = Timer(const Duration(milliseconds: 500), () {
      if (_currentSearch != value) {
        setState(() {
          _currentSearch = value;
          _currentPage = 1; // Reset to first page when searching
        });
        _loadEmployees();
      }
    });
  }

  void _deleteEmployee(Employee employee) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف', style: TextStyle(fontFamily: 'Cairo')),
        content: Text(
          'هل أنت متأكد من حذف الموظف "${employee.fullName}"؟',
          style: const TextStyle(fontFamily: 'Cairo'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('حذف', style: TextStyle(fontFamily: 'Cairo')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await EmployeeService.deleteEmployee(employee.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف الموظف "${employee.fullName}" بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }

        _loadEmployees(); // Refresh the list
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الموظف: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة الموظفين',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5D6E35), // Costa del sol
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadEmployees,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.adminPrimary.withValues(alpha: 0.1),
              AppColors.background,
            ],
          ),
        ),
        child: Column(
          children: [
            // Header with stats
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.people,
                        size: 30,
                        color: Color(0xFF5D6E35),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'إدارة الموظفين',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2C3E50),
                              ),
                            ),
                            Text(
                              _currentSearch.isEmpty
                                  ? 'إجمالي الموظفين: $_totalEmployees'
                                  : 'نتائج البحث: $_totalEmployees موظف',
                              style: const TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                            if (_totalEmployees > 0)
                              Text(
                                'صفحة $_currentPage من $_totalPages',
                                style: const TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                        ),
                      ),
                      CustomButton(
                        text: 'إضافة موظف',
                        onPressed: () async {
                          final result = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AddEmployeeScreen(),
                            ),
                          );

                          if (result == true) {
                            _loadEmployees(); // Refresh the list
                          }
                        },
                        backgroundColor: const Color(0xFF5D6E35),
                        textColor: Colors.white,
                        icon: Icons.add,
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Search
                  CustomTextField(
                    controller: _searchController,
                    labelText: 'البحث عن موظف',
                    hintText: 'ابحث عن موظف...',
                    prefixIcon: Icons.search,
                    onChanged: _onSearchChanged,
                  ),
                ],
              ),
            ),

            // Employees list
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Color(0xFF5D6E35)),
                          SizedBox(height: 20),
                          Text(
                            'جاري تحميل الموظفين...',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 16,
                              color: Color(0xFF5D6E35),
                            ),
                          ),
                        ],
                      ),
                    )
                  : _filteredEmployees.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 80,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 20),
                          Text(
                            'لا توجد موظفين',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Column(
                      children: [
                        // Employees list
                        Expanded(
                          child: ListView.builder(
                            padding: const EdgeInsets.all(20),
                            itemCount: _currentPageEmployees.length,
                            itemBuilder: (context, index) {
                              final employee = _currentPageEmployees[index];
                              return _buildEmployeeCard(employee);
                            },
                          ),
                        ),

                        // Pagination controls
                        if (_totalPages > 1) _buildPaginationControls(),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeCard(Employee employee) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: const Color(0xFF5D6E35).withValues(alpha: 0.1),
              child: Text(
                employee.fullName.isNotEmpty ? employee.fullName[0] : 'M',
                style: const TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF5D6E35),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // Employee info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Full name
                  Text(
                    employee.fullName,
                    style: const TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2C3E50),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Username
                  Text(
                    'اسم المستخدم: ${employee.username}',
                    style: const TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 13,
                      color: Colors.grey,
                    ),
                  ),

                  const SizedBox(height: 2),

                  // Phone
                  if (employee.phone != null && employee.phone!.isNotEmpty)
                    Text(
                      'الهاتف: ${employee.phone}',
                      style: const TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 13,
                        color: Colors.grey,
                      ),
                    ),
                ],
              ),
            ),

            // Menu button
            PopupMenuButton<String>(
              onSelected: (value) async {
                switch (value) {
                  case 'edit':
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            EditEmployeeScreen(employee: employee),
                      ),
                    );

                    if (result == true) {
                      _loadEmployees(); // Refresh the list
                    }
                    break;
                  case 'delete':
                    _deleteEmployee(employee);
                    break;
                }
              },
              icon: const Icon(Icons.more_vert, color: Colors.grey, size: 20),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, color: Colors.blue, size: 18),
                      SizedBox(width: 8),
                      Text(
                        'تعديل',
                        style: TextStyle(fontFamily: 'Cairo', fontSize: 14),
                      ),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red, size: 18),
                      SizedBox(width: 8),
                      Text(
                        'حذف',
                        style: TextStyle(fontFamily: 'Cairo', fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          TextButton.icon(
            onPressed: _currentPage > 1 ? _previousPage : null,
            icon: const Icon(Icons.arrow_back_ios, size: 16),
            label: const Text(
              'السابق',
              style: TextStyle(fontFamily: 'Cairo', fontSize: 14),
            ),
            style: TextButton.styleFrom(
              foregroundColor: _currentPage > 1
                  ? const Color(0xFF5D6E35)
                  : Colors.grey,
            ),
          ),

          // Page info
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF5D6E35).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'صفحة $_currentPage من $_totalPages',
              style: const TextStyle(
                fontFamily: 'Cairo',
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF5D6E35),
              ),
            ),
          ),

          // Next button
          TextButton.icon(
            onPressed: _currentPage < _totalPages ? _nextPage : null,
            icon: const Icon(Icons.arrow_forward_ios, size: 16),
            label: const Text(
              'التالي',
              style: TextStyle(fontFamily: 'Cairo', fontSize: 14),
            ),
            style: TextButton.styleFrom(
              foregroundColor: _currentPage < _totalPages
                  ? const Color(0xFF5D6E35)
                  : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchDebounce?.cancel();
    _searchController.dispose();
    super.dispose();
  }
}
