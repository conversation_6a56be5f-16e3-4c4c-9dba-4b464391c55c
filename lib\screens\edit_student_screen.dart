import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/student_service.dart';
import '../utils/constants.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/logo_widget.dart';

class EditStudentScreen extends StatefulWidget {
  final Student student;

  const EditStudentScreen({super.key, required this.student});

  @override
  State<EditStudentScreen> createState() => _EditStudentScreenState();
}

class _EditStudentScreenState extends State<EditStudentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  String? _selectedSpecialization;
  String? _selectedSection;
  String? _selectedClass;
  String? _selectedLevel;
  String? _selectedResult;
  String? _selectedNationality;
  bool _isActive = true;
  bool _isLoading = false;
  bool _isLoadingFormData = true;

  // Form data
  List<String> _specializations = [];
  List<String> _sections = [];
  List<String> _classes = [];
  List<String> _levels = [];
  List<String> _results = [];
  List<String> _nationalities = [];

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _loadFormData();
  }

  void _initializeForm() {
    // Initialize form with student data
    _usernameController.text = widget.student.username;
    _fullNameController.text = widget.student.fullName;
    _selectedNationality = widget.student.nationality.isEmpty
        ? 'لبناني'
        : widget.student.nationality;
    _phoneController.text = widget.student.phone ?? '';
    _passwordController.text = 'stu123'; // Default password

    _selectedSpecialization = widget.student.specialization;
    _selectedSection = widget.student.section;
    _selectedClass = widget.student.studentClass;
    _selectedLevel = widget.student.level;
    _selectedResult = widget.student.result;
    _isActive = widget.student.isActive;
  }

  Future<void> _loadFormData() async {
    try {
      setState(() => _isLoadingFormData = true);

      if (kDebugMode) {
        print('📊 Loading form data for edit...');
      }

      final formData = await StudentService.getFormData();

      setState(() {
        _specializations = formData['specializations'] as List<String>;
        _sections = formData['sections'] as List<String>;
        _classes = formData['classes'] as List<String>;
        _levels = formData['levels'] as List<String>;
        _results = formData['results'] as List<String>;
        _nationalities = formData['nationalities'] as List<String>;
        _isLoadingFormData = false;
      });

      if (kDebugMode) {
        print('✅ Form data loaded successfully');
        print('   Specializations: ${_specializations.length}');
        print('   Sections: ${_sections.length}');
        print('   Classes: ${_classes.length}');
        print('   Levels: ${_levels.length}');
        print('   Results: ${_results.length}');
        print('   Nationalities: ${_nationalities.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading form data: $e');
      }

      setState(() => _isLoadingFormData = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات النموذج: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _updateStudent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      if (kDebugMode) {
        print('📝 Updating student: ${widget.student.id}');
      }

      final studentData = {
        'username': _usernameController.text.trim(),
        'full_name': _fullNameController.text.trim(),
        'nationality': _selectedNationality ?? 'لبناني',
        'phone': _phoneController.text.trim(),
        'specialization': _selectedSpecialization ?? '',
        'section': _selectedSection ?? '',
        'class': _selectedClass ?? '',
        'level': _selectedLevel ?? '',
        'result': _selectedResult,
        'password': _passwordController.text.trim(),
        'is_active': _isActive,
      };

      if (kDebugMode) {
        print('📤 Sending update data: $studentData');
      }

      final updatedStudent = await StudentService.updateStudent(
        widget.student.id,
        studentData,
      );

      if (updatedStudent != null) {
        // Clear cache to refresh data
        StudentService.clearCache();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديث بيانات ${updatedStudent.fullName} بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop(updatedStudent); // Return updated student
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('فشل في تحديث بيانات الطالب'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Update student error: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الطالب: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.adminPrimary,
        foregroundColor: Colors.white,
        title: Text(
          'تعديل الطالب: ${widget.student.fullName}',
          style: const TextStyle(
            fontFamily: 'Cairo',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
      ),
      body: _isLoadingFormData
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Color(0xFF5D6E35)),
                  SizedBox(height: 20),
                  Text(
                    'جاري تحميل بيانات النموذج...',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Logo
                  const LogoWidget(),
                  const SizedBox(height: 30),

                  // Form Card
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.cardBackground,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.costaDelSol.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Text(
                            'تعديل بيانات الطالب',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: AppColors.adminPrimary,
                              fontFamily: 'Cairo',
                            ),
                          ),
                          const SizedBox(height: 24),

                          // Username field (read-only)
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.person,
                                  color: Colors.grey[600],
                                  size: 22,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'اسم المستخدم',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        widget.student.username,
                                        style: const TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 16,
                                          color: Color(0xFF2C3E50),
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Text(
                                    'غير قابل للتعديل',
                                    style: TextStyle(
                                      fontFamily: 'Cairo',
                                      fontSize: 10,
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Full name field
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: CustomTextField(
                              controller: _fullNameController,
                              labelText: 'الاسم الكامل',
                              hintText: 'أدخل الاسم الكامل للطالب',
                              prefixIcon: Icons.person_outline,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الاسم الكامل مطلوب';
                                }
                                if (value.length < 3) {
                                  return 'الاسم يجب أن يكون 3 أحرف على الأقل';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(height: 20),

                          // Nationality dropdown
                          _buildDropdownField(
                            label: 'الجنسية',
                            value: _selectedNationality,
                            items: _nationalities,
                            onChanged: (value) =>
                                setState(() => _selectedNationality = value),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الجنسية مطلوبة';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Phone field
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: CustomTextField(
                              controller: _phoneController,
                              labelText: 'رقم الهاتف',
                              hintText: 'أدخل رقم الهاتف (اختياري)',
                              prefixIcon: Icons.phone_android,
                              keyboardType: TextInputType.phone,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Specialization dropdown
                          _buildDropdownField(
                            label: 'التخصص',
                            value: _selectedSpecialization,
                            items: _specializations,
                            onChanged: (value) =>
                                setState(() => _selectedSpecialization = value),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'التخصص مطلوب';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Section dropdown
                          _buildDropdownField(
                            label: 'الشعبة',
                            value: _selectedSection,
                            items: _sections,
                            onChanged: (value) =>
                                setState(() => _selectedSection = value),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الشعبة مطلوبة';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Class dropdown
                          _buildDropdownField(
                            label: 'الصف',
                            value: _selectedClass,
                            items: _classes,
                            onChanged: (value) =>
                                setState(() => _selectedClass = value),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الصف مطلوب';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Level dropdown
                          _buildDropdownField(
                            label: 'المستوى',
                            value: _selectedLevel,
                            items: _levels,
                            onChanged: (value) =>
                                setState(() => _selectedLevel = value),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'المستوى مطلوب';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Result dropdown (optional)
                          _buildDropdownField(
                            label: 'النتيجة (اختياري)',
                            value: _selectedResult,
                            items: _results,
                            onChanged: (value) =>
                                setState(() => _selectedResult = value),
                            isRequired: false,
                          ),
                          const SizedBox(height: 16),

                          // Password field
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: CustomTextField(
                              controller: _passwordController,
                              labelText: 'كلمة المرور',
                              hintText: 'أدخل كلمة المرور الجديدة',
                              prefixIcon: Icons.lock_outline,
                              obscureText: true,
                              suffixIcon: Container(
                                margin: const EdgeInsets.all(8),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF5D6E35,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: const Text(
                                  'افتراضي: stu123',
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 10,
                                    color: Color(0xFF5D6E35),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'كلمة المرور مطلوبة';
                                }
                                if (value.length < 3) {
                                  return 'كلمة المرور يجب أن تكون 3 أحرف على الأقل';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Active status switch
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[300]!),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: _isActive
                                        ? AppColors.success.withValues(
                                            alpha: 0.1,
                                          )
                                        : Colors.grey.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    _isActive
                                        ? Icons.check_circle
                                        : Icons.cancel,
                                    color: _isActive
                                        ? AppColors.success
                                        : Colors.grey[600],
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'حالة الطالب',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFF2C3E50),
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        _isActive
                                            ? 'نشط - يمكن للطالب تسجيل الدخول'
                                            : 'غير نشط - لا يمكن تسجيل الدخول',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 12,
                                          color: _isActive
                                              ? AppColors.success
                                              : Colors.grey[600],
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value: _isActive,
                                  onChanged: (value) {
                                    setState(() {
                                      _isActive = value;
                                    });
                                  },
                                  activeColor: AppColors.success,
                                  activeTrackColor: AppColors.success
                                      .withValues(alpha: 0.3),
                                  inactiveThumbColor: Colors.grey[400],
                                  inactiveTrackColor: Colors.grey[300],
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 32),

                          // Action buttons
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.grey[200]!),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    height: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withValues(
                                            alpha: 0.1,
                                          ),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: CustomButton(
                                      text: 'إلغاء',
                                      onPressed: () =>
                                          Navigator.of(context).pop(),
                                      backgroundColor: AppColors.error,
                                      icon: Icons.cancel_outlined,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  flex: 2,
                                  child: Container(
                                    height: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.success.withValues(
                                            alpha: 0.3,
                                          ),
                                          blurRadius: 12,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: CustomButton(
                                      text: _isLoading
                                          ? 'جاري التحديث...'
                                          : 'تحديث الطالب',
                                      onPressed: _isLoading
                                          ? null
                                          : _updateStudent,
                                      backgroundColor: AppColors.success,
                                      icon: _isLoading
                                          ? null
                                          : Icons.save_outlined,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    String? Function(String?)? validator,
    bool isRequired = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Cairo',
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          hint: Text('اختر $label'),
          items: items.map((item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item, style: const TextStyle(fontFamily: 'Cairo')),
            );
          }).toList(),
          onChanged: onChanged,
          validator: isRequired ? validator : null,
        ),
      ],
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
