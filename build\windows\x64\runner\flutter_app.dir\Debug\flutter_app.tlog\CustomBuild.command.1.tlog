^C:\USERS\<USER>\DESKTOP\APPS\FLUTTER-APP\WINDOWS\RUNNER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/apps/flutter-app/windows -BC:/Users/<USER>/Desktop/apps/flutter-app/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/apps/flutter-app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
