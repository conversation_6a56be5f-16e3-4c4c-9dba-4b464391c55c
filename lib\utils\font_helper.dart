import 'package:flutter/material.dart';

class FontHelper {
  // Default font family - will fallback to system font if Cairo is not available
  static const String defaultFontFamily = 'Cairo';
  
  // Get text style with Cairo font or system fallback
  static TextStyle getTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
      fontFamily: defaultFontFamily,
    );
  }
  
  // Predefined text styles with Cairo font
  static TextStyle get headlineLarge => getTextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
  );
  
  static TextStyle get headlineMedium => getTextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
  );
  
  static TextStyle get headlineSmall => getTextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
  );
  
  static TextStyle get titleLarge => getTextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
  );
  
  static TextStyle get titleMedium => getTextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );
  
  static TextStyle get titleSmall => getTextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );
  
  static TextStyle get bodyLarge => getTextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );
  
  static TextStyle get bodyMedium => getTextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
  );
  
  static TextStyle get bodySmall => getTextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
  );
  
  static TextStyle get labelLarge => getTextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );
  
  static TextStyle get labelMedium => getTextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );
  
  static TextStyle get labelSmall => getTextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w500,
  );
  
  // Arabic text specific styles
  static TextStyle get arabicHeadline => getTextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    height: 1.3, // Better line height for Arabic
  );
  
  static TextStyle get arabicTitle => getTextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );
  
  static TextStyle get arabicBody => getTextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    height: 1.4,
  );
  
  // Button text style
  static TextStyle get buttonText => getTextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
  );
  
  // App bar title style
  static TextStyle get appBarTitle => getTextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );
}
