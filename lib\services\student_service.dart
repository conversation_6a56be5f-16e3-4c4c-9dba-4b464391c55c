import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class StudentService {
  static const String _baseUrl = ApiConstants.students;
  
  static final Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Get authorization headers with token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    
    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    return headers;
  }

  // Get all students
  static Future<List<Student>> getAllStudents() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse(_baseUrl),
        headers: headers,
      );

      if (kDebugMode) {
        print('Get students response: ${response.statusCode} - ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        // Handle different response structures
        List<dynamic> studentsJson;
        if (data is Map && data.containsKey('data')) {
          studentsJson = data['data'];
        } else if (data is Map && data.containsKey('students')) {
          studentsJson = data['students'];
        } else if (data is List) {
          studentsJson = data;
        } else {
          if (kDebugMode) {
            print('Unexpected response structure: $data');
          }
          return [];
        }

        return studentsJson.map((json) => Student.fromJson(json)).toList();
      } else {
        if (kDebugMode) {
          print('Failed to get students: ${response.statusCode} - ${response.body}');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get students error: $e');
      }
      return [];
    }
  }

  // Get student by ID
  static Future<Student?> getStudentById(int id) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$id'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        // Handle different response structures
        Map<String, dynamic> studentJson;
        if (data is Map && data.containsKey('data')) {
          studentJson = data['data'];
        } else if (data is Map && data.containsKey('student')) {
          studentJson = data['student'];
        } else {
          studentJson = data;
        }

        return Student.fromJson(studentJson);
      } else {
        if (kDebugMode) {
          print('Failed to get student: ${response.statusCode} - ${response.body}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get student error: $e');
      }
      return null;
    }
  }

  // Create new student
  static Future<Student?> createStudent(Map<String, dynamic> studentData) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: headers,
        body: jsonEncode(studentData),
      );

      if (kDebugMode) {
        print('Create student response: ${response.statusCode} - ${response.body}');
      }

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        // Handle different response structures
        Map<String, dynamic> studentJson;
        if (data is Map && data.containsKey('data')) {
          studentJson = data['data'];
        } else if (data is Map && data.containsKey('student')) {
          studentJson = data['student'];
        } else {
          studentJson = data;
        }

        return Student.fromJson(studentJson);
      } else {
        if (kDebugMode) {
          print('Failed to create student: ${response.statusCode} - ${response.body}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Create student error: $e');
      }
      return null;
    }
  }

  // Update student
  static Future<Student?> updateStudent(int id, Map<String, dynamic> studentData) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.put(
        Uri.parse('$_baseUrl/$id'),
        headers: headers,
        body: jsonEncode(studentData),
      );

      if (kDebugMode) {
        print('Update student response: ${response.statusCode} - ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        // Handle different response structures
        Map<String, dynamic> studentJson;
        if (data is Map && data.containsKey('data')) {
          studentJson = data['data'];
        } else if (data is Map && data.containsKey('student')) {
          studentJson = data['student'];
        } else {
          studentJson = data;
        }

        return Student.fromJson(studentJson);
      } else {
        if (kDebugMode) {
          print('Failed to update student: ${response.statusCode} - ${response.body}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Update student error: $e');
      }
      return null;
    }
  }

  // Delete student
  static Future<bool> deleteStudent(int id) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.delete(
        Uri.parse('$_baseUrl/$id'),
        headers: headers,
      );

      if (kDebugMode) {
        print('Delete student response: ${response.statusCode} - ${response.body}');
      }

      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      if (kDebugMode) {
        print('Delete student error: $e');
      }
      return false;
    }
  }

  // Get available classes
  static Future<List<String>> getAvailableClasses() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('${_baseUrl}/classes/list'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        // Handle different response structures
        List<dynamic> classesJson;
        if (data is Map && data.containsKey('data')) {
          classesJson = data['data'];
        } else if (data is Map && data.containsKey('classes')) {
          classesJson = data['classes'];
        } else if (data is List) {
          classesJson = data;
        } else {
          return [];
        }

        return classesJson.map((item) => item.toString()).toList();
      } else {
        if (kDebugMode) {
          print('Failed to get classes: ${response.statusCode} - ${response.body}');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get classes error: $e');
      }
      return [];
    }
  }

  // Bulk import students
  static Future<Map<String, dynamic>> importStudents(List<Map<String, dynamic>> studentsData) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse('$_baseUrl/bulk-import'),
        headers: headers,
        body: jsonEncode({'students': studentsData}),
      );

      if (kDebugMode) {
        print('Import students response: ${response.statusCode} - ${response.body}');
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        return {
          'success': false,
          'message': 'فشل في استيراد الطلاب',
          'error': response.body,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Import students error: $e');
      }
      return {
        'success': false,
        'message': 'خطأ في الاتصال',
        'error': e.toString(),
      };
    }
  }
}
