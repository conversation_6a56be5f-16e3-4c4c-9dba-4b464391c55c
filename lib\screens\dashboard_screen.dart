import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';
import '../widgets/logo_widget.dart';
import 'login_selection_screen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final userType = authService.userType;
        final currentUser = authService.currentUser;

        Color primaryColor;
        IconData userIcon;
        String userTitle;

        switch (userType) {
          case 'admin':
            primaryColor = AppColors.adminPrimary;
            userIcon = Icons.admin_panel_settings;
            userTitle = 'لوحة تحكم الأدمن';
            break;
          case 'student':
            primaryColor = AppColors.studentPrimary;
            userIcon = Icons.school;
            userTitle = 'لوحة تحكم الطالب';
            break;
          case 'employee':
            primaryColor = AppColors.employeePrimary;
            userIcon = Icons.work;
            userTitle = 'لوحة تحكم الموظف';
            break;
          default:
            primaryColor = AppColors.costaDelSol;
            userIcon = Icons.person;
            userTitle = 'لوحة التحكم';
        }

        return Scaffold(
          appBar: AppBar(
            title: Row(
              children: [
                // Logo in app bar
                Container(
                  width: 32,
                  height: 32,
                  margin: const EdgeInsets.only(right: 12),
                  child: const LogoWidget(size: 32, showText: false),
                ),
                Text(userTitle),
              ],
            ),
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            automaticallyImplyLeading: false,
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) async {
                  if (value == 'logout') {
                    await authService.logout();
                    if (context.mounted) {
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const LoginSelectionScreen(),
                        ),
                        (route) => false,
                      );
                    }
                  }
                },
                itemBuilder: (BuildContext context) => [
                  const PopupMenuItem<String>(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Colors.red),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج'),
                      ],
                    ),
                  ),
                ],
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Icon(Icons.more_vert),
                ),
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  primaryColor.withValues(alpha: 0.1),
                  AppColors.background,
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.cardBackground,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.costaDelSol.withValues(alpha: 0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              userIcon,
                              size: 40,
                              color: primaryColor,
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'أهلاً وسهلاً بك!',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  authService.getUserDisplayName(),
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  _getUserTypeArabic(userType),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 30),

                    // User Information
                    Text(
                      'معلومات الحساب',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),

                    const SizedBox(height: 15),

                    _buildUserInfoCard(currentUser, userType, primaryColor),

                    const SizedBox(height: 30),

                    // Quick Actions
                    Text(
                      'الإجراءات السريعة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),

                    const SizedBox(height: 15),

                    Expanded(
                      child: GridView.count(
                        crossAxisCount: 2,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,
                        children: _buildQuickActions(userType, primaryColor),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserInfoCard(
    dynamic user,
    String? userType,
    Color primaryColor,
  ) {
    List<Widget> infoItems = [];

    if (user is Admin) {
      infoItems = [
        _buildInfoItem('البريد الإلكتروني', user.email, Icons.email),
        if (user.name != null)
          _buildInfoItem('الاسم', user.name!, Icons.person),
        _buildInfoItem('معرف الأدمن', user.id.toString(), Icons.badge),
        if (user.createdAt != null)
          _buildInfoItem(
            'تاريخ الإنشاء',
            _formatDate(user.createdAt!),
            Icons.calendar_today,
          ),
      ];
    } else if (user is User) {
      infoItems = [
        _buildInfoItem('البريد الإلكتروني', user.email, Icons.email),
        if (user.name != null)
          _buildInfoItem('الاسم', user.name!, Icons.person),
        _buildInfoItem('معرف المستخدم', user.id.toString(), Icons.badge),
      ];
    } else if (user is Student) {
      infoItems = [
        _buildInfoItem('اسم المستخدم', user.username, Icons.person),
        if (user.name != null)
          _buildInfoItem('الاسم', user.name!, Icons.person),
        if (user.email != null)
          _buildInfoItem('البريد الإلكتروني', user.email!, Icons.email),
        _buildInfoItem('معرف الطالب', user.id.toString(), Icons.badge),
      ];
    } else if (user is Employee) {
      infoItems = [
        _buildInfoItem('اسم المستخدم', user.username, Icons.person),
        if (user.name != null)
          _buildInfoItem('الاسم', user.name!, Icons.person),
        if (user.email != null)
          _buildInfoItem('البريد الإلكتروني', user.email!, Icons.email),
        if (user.department != null)
          _buildInfoItem('القسم', user.department!, Icons.business),
        _buildInfoItem('معرف الموظف', user.id.toString(), Icons.badge),
      ];
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: AppColors.costaDelSol.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(children: infoItems),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.textSecondary),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: AppColors.textPrimary)),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildQuickActions(String? userType, Color primaryColor) {
    List<Map<String, dynamic>> actions = [];

    switch (userType) {
      case 'admin':
        actions = [
          {
            'title': 'إدارة الطلاب',
            'icon': Icons.school,
            'color': AppColors.locust,
          },
          {
            'title': 'إعدادات النظام',
            'icon': Icons.settings,
            'color': AppColors.gurkha,
          },
          {
            'title': 'التقارير',
            'icon': Icons.analytics,
            'color': AppColors.avocado,
          },
          {
            'title': 'الإشعارات',
            'icon': Icons.notifications,
            'color': AppColors.chino,
          },
        ];
        break;
      case 'student':
        actions = [
          {'title': 'مقرراتي', 'icon': Icons.book, 'color': AppColors.locust},
          {
            'title': 'الواجبات',
            'icon': Icons.assignment,
            'color': AppColors.chino,
          },
          {'title': 'الدرجات', 'icon': Icons.grade, 'color': AppColors.avocado},
          {
            'title': 'الجدول الزمني',
            'icon': Icons.schedule,
            'color': AppColors.gurkha,
          },
        ];
        break;
      case 'employee':
        actions = [
          {'title': 'مهامي', 'icon': Icons.task, 'color': AppColors.locust},
          {
            'title': 'جدول الحضور',
            'icon': Icons.access_time,
            'color': AppColors.avocado,
          },
          {
            'title': 'المستندات',
            'icon': Icons.folder,
            'color': AppColors.chino,
          },
          {'title': 'الفريق', 'icon': Icons.group, 'color': AppColors.gurkha},
        ];
        break;
      default:
        actions = [
          {
            'title': 'الملف الشخصي',
            'icon': Icons.person,
            'color': AppColors.locust,
          },
          {
            'title': 'الإعدادات',
            'icon': Icons.settings,
            'color': AppColors.gurkha,
          },
        ];
    }

    return actions
        .map(
          (action) => _buildActionCard(
            action['title'],
            action['icon'],
            action['color'],
          ),
        )
        .toList();
  }

  Widget _buildActionCard(String title, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: AppColors.costaDelSol.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: () {
            // TODO: Implement action functionality
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 30, color: color),
                ),
                const SizedBox(height: 10),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to format date
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  // Helper method to get user type in Arabic
  String _getUserTypeArabic(String? userType) {
    switch (userType) {
      case 'admin':
        return 'حساب الأدمن';
      case 'student':
        return 'حساب الطالب';
      case 'employee':
        return 'حساب الموظف';
      default:
        return 'حساب المستخدم';
    }
  }
}
