import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';
import 'login_selection_screen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final userType = authService.userType;
        final currentUser = authService.currentUser;
        
        Color primaryColor;
        IconData userIcon;
        String userTitle;
        
        switch (userType) {
          case 'admin':
            primaryColor = Colors.red;
            userIcon = Icons.admin_panel_settings;
            userTitle = 'Admin Dashboard';
            break;
          case 'student':
            primaryColor = Colors.green;
            userIcon = Icons.school;
            userTitle = 'Student Dashboard';
            break;
          case 'employee':
            primaryColor = Colors.orange;
            userIcon = Icons.work;
            userTitle = 'Employee Dashboard';
            break;
          default:
            primaryColor = Colors.blue;
            userIcon = Icons.person;
            userTitle = 'Dashboard';
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(userTitle),
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            automaticallyImplyLeading: false,
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) async {
                  if (value == 'logout') {
                    await authService.logout();
                    if (context.mounted) {
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const LoginSelectionScreen(),
                        ),
                        (route) => false,
                      );
                    }
                  }
                },
                itemBuilder: (BuildContext context) => [
                  const PopupMenuItem<String>(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Logout'),
                      ],
                    ),
                  ),
                ],
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Icon(Icons.more_vert),
                ),
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  primaryColor.withOpacity(0.1),
                  Colors.white,
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              userIcon,
                              size: 40,
                              color: primaryColor,
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Welcome back!',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  authService.getUserDisplayName(),
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  '${userType?.toUpperCase()} ACCOUNT',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 30),
                    
                    // User Information
                    Text(
                      'Account Information',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    
                    const SizedBox(height: 15),
                    
                    _buildUserInfoCard(currentUser, userType, primaryColor),
                    
                    const SizedBox(height: 30),
                    
                    // Quick Actions
                    Text(
                      'Quick Actions',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    
                    const SizedBox(height: 15),
                    
                    Expanded(
                      child: GridView.count(
                        crossAxisCount: 2,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,
                        children: _buildQuickActions(userType, primaryColor),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserInfoCard(dynamic user, String? userType, Color primaryColor) {
    List<Widget> infoItems = [];
    
    if (user is User) {
      infoItems = [
        _buildInfoItem('Email', user.email, Icons.email),
        if (user.name != null) _buildInfoItem('Name', user.name!, Icons.person),
        _buildInfoItem('User ID', user.id.toString(), Icons.badge),
      ];
    } else if (user is Student) {
      infoItems = [
        _buildInfoItem('Username', user.username, Icons.person),
        if (user.name != null) _buildInfoItem('Name', user.name!, Icons.person),
        if (user.email != null) _buildInfoItem('Email', user.email!, Icons.email),
        _buildInfoItem('Student ID', user.id.toString(), Icons.badge),
      ];
    } else if (user is Employee) {
      infoItems = [
        _buildInfoItem('Username', user.username, Icons.person),
        if (user.name != null) _buildInfoItem('Name', user.name!, Icons.person),
        if (user.email != null) _buildInfoItem('Email', user.email!, Icons.email),
        if (user.department != null) _buildInfoItem('Department', user.department!, Icons.business),
        _buildInfoItem('Employee ID', user.id.toString(), Icons.badge),
      ];
    }
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: infoItems,
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey.shade800),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildQuickActions(String? userType, Color primaryColor) {
    List<Map<String, dynamic>> actions = [];
    
    switch (userType) {
      case 'admin':
        actions = [
          {'title': 'Manage Users', 'icon': Icons.people, 'color': Colors.blue},
          {'title': 'System Settings', 'icon': Icons.settings, 'color': Colors.grey},
          {'title': 'Reports', 'icon': Icons.analytics, 'color': Colors.green},
          {'title': 'Notifications', 'icon': Icons.notifications, 'color': Colors.orange},
        ];
        break;
      case 'student':
        actions = [
          {'title': 'My Courses', 'icon': Icons.book, 'color': Colors.blue},
          {'title': 'Assignments', 'icon': Icons.assignment, 'color': Colors.orange},
          {'title': 'Grades', 'icon': Icons.grade, 'color': Colors.green},
          {'title': 'Schedule', 'icon': Icons.schedule, 'color': Colors.purple},
        ];
        break;
      case 'employee':
        actions = [
          {'title': 'My Tasks', 'icon': Icons.task, 'color': Colors.blue},
          {'title': 'Time Sheet', 'icon': Icons.access_time, 'color': Colors.green},
          {'title': 'Documents', 'icon': Icons.folder, 'color': Colors.orange},
          {'title': 'Team', 'icon': Icons.group, 'color': Colors.purple},
        ];
        break;
      default:
        actions = [
          {'title': 'Profile', 'icon': Icons.person, 'color': Colors.blue},
          {'title': 'Settings', 'icon': Icons.settings, 'color': Colors.grey},
        ];
    }
    
    return actions.map((action) => _buildActionCard(
      action['title'],
      action['icon'],
      action['color'],
    )).toList();
  }

  Widget _buildActionCard(String title, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: () {
            // TODO: Implement action functionality
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    size: 30,
                    color: color,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
