import 'dart:async';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/student_service.dart';
import '../services/excel_service.dart';
import '../utils/constants.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import 'add_student_screen.dart';
import '../widgets/logo_widget.dart';

class ManageStudentsScreen extends StatefulWidget {
  const ManageStudentsScreen({super.key});

  @override
  State<ManageStudentsScreen> createState() => _ManageStudentsScreenState();
}

class _ManageStudentsScreenState extends State<ManageStudentsScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Student> _students = [];
  List<Student> _filteredStudents = [];
  bool _isLoading = false;

  // Pagination variables
  int _currentPage = 1;
  int _totalPages = 1;
  int _totalStudents = 0;
  final int _perPage = 20;
  String _currentSearch = '';

  // Debounce timer for search
  Timer? _searchTimer;

  @override
  void initState() {
    super.initState();
    _loadStudents();
  }

  // Load students from API with pagination
  Future<void> _loadStudents({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
    }

    setState(() => _isLoading = true);

    try {
      final result = await StudentService.getStudents(
        page: _currentPage,
        perPage: _perPage,
        search: _currentSearch.isEmpty ? null : _currentSearch,
      );

      if (mounted) {
        setState(() {
          _students = result['students'] as List<Student>;
          _filteredStudents = _students; // No local filtering needed
          _totalStudents = result['total'] as int;
          _totalPages = result['last_page'] as int;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الطلاب: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Refresh students list
  Future<void> _refreshStudents() async {
    await _loadStudents(isRefresh: true);
  }

  // Delete student
  Future<void> _deleteStudent(Student student) async {
    try {
      setState(() => _isLoading = true);

      final success = await StudentService.deleteStudent(student.id);

      if (mounted) {
        setState(() => _isLoading = false);

        if (success) {
          // Reload the current page to reflect changes
          await _loadStudents();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف ${student.fullName} بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف ${student.fullName}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الطالب: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Search with debounce for server-side search
  void _filterStudents(String query) {
    // Cancel previous timer
    _searchTimer?.cancel();

    // Start new timer
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      if (_currentSearch != query) {
        setState(() {
          _currentSearch = query;
          _currentPage = 1; // Reset to first page
        });
        _loadStudents();
      }
    });
  }

  Future<void> _addNewStudent() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddStudentScreen()),
    );

    // If student was added successfully, refresh the list
    if (result == true) {
      _refreshStudents();
    }
  }

  Future<void> _importFromExcel() async {
    try {
      setState(() => _isLoading = true);

      if (kDebugMode) {
        print('🔍 Opening file picker for Excel import...');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.bytes == null) {
          throw Exception('لا يمكن قراءة الملف');
        }

        if (kDebugMode) {
          print('📁 File selected: ${file.name}, Size: ${file.size} bytes');
        }

        // Show processing dialog
        if (mounted) {
          _showProcessingDialog();
        }

        // Process Excel file
        final importResult = await ExcelService.importStudentsFromExcel(
          file.bytes!,
          file.name,
        );

        // Close processing dialog
        if (mounted) {
          Navigator.of(context).pop();
        }

        // Show result
        if (mounted) {
          _showImportResult(importResult);
        }

        // Refresh students list if import was successful
        if (importResult['success'] == true &&
            importResult['importedCount'] > 0) {
          await _refreshStudents();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Excel import error: $e');
      }

      if (mounted) {
        // Close processing dialog if open
        Navigator.of(context).popUntil((route) => route.isFirst);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في استيراد الملف: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showProcessingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: Color(0xFF5D6E35)),
            const SizedBox(height: 20),
            const Text(
              'جاري معالجة ملف Excel...',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'يرجى الانتظار',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadSampleExcel() async {
    try {
      if (kDebugMode) {
        print('📥 Generating sample Excel file...');
      }

      // For web, we'll show instructions instead of direct download
      if (kIsWeb) {
        _showSampleExcelInstructions();
      } else {
        // Generate sample Excel file
        final excelBytes = ExcelService.generateSampleExcel();

        // For mobile/desktop, save to downloads
        // This would require additional packages like path_provider
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إنشاء نموذج Excel بحجم ${excelBytes.length} بايت',
              ),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating sample Excel: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء نموذج Excel: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showSampleExcelInstructions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: Color(0xFF5D6E35)),
            SizedBox(width: 8),
            Text(
              'نموذج ملف Excel',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'يجب أن يحتوي ملف Excel على الأعمدة التالية بنفس الترتيب:',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: ExcelService.expectedHeaders.asMap().entries.map((
                    entry,
                  ) {
                    final index = entry.key + 1;
                    final header = entry.value;
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        '$index. $header',
                        style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),

              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF5D6E35).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📝 ملاحظات مهمة:',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF5D6E35),
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• الصف الأول يجب أن يحتوي على أسماء الأعمدة بالإنجليزية\n'
                      '• username: اسم المستخدم (مطلوب)\n'
                      '• full_name: الاسم الكامل (مطلوب)\n'
                      '• nationality: الجنسية (افتراضي: لبناني)\n'
                      '• phone: رقم الهاتف\n'
                      '• specialization: التخصص (مطلوب)\n'
                      '• section: الشعبة (مطلوب)\n'
                      '• class: الصف (مطلوب)\n'
                      '• level: المستوى (مطلوب)\n'
                      '• result: النتيجة (اختياري)\n'
                      '• password: كلمة المرور (افتراضي: stu123)\n'
                      '• is_active: فعال (1 أو 0، افتراضي: 1)',
                      style: TextStyle(fontFamily: 'Cairo', fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'موافق',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showImportResult(Map<String, dynamic> result) {
    final bool success = result['success'] ?? false;
    final String message = result['message'] ?? '';
    final int importedCount = result['importedCount'] ?? 0;
    final int parsedCount = result['parsedCount'] ?? 0;
    final List<String> parseErrors = result['parseErrors'] ?? [];
    final List<String> importErrors = result['importErrors'] ?? [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              success ? Icons.check_circle : Icons.error,
              color: success ? AppColors.success : AppColors.error,
            ),
            const SizedBox(width: 8),
            Text(
              success ? 'نجح الاستيراد' : 'فشل الاستيراد',
              style: const TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                message,
                style: const TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),

              if (success && importedCount > 0) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '✅ تم استيراد $importedCount طالب بنجاح',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (parsedCount != importedCount)
                        Text(
                          'تم تحليل $parsedCount صف من الملف',
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 14,
                          ),
                        ),
                    ],
                  ),
                ),
              ],

              if (parseErrors.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'أخطاء في تحليل البيانات:',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                    color: AppColors.error,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: parseErrors
                        .take(5)
                        .map(
                          (error) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• $error',
                              style: const TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
                if (parseErrors.length > 5)
                  Text(
                    'و ${parseErrors.length - 5} أخطاء أخرى...',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
              ],

              if (importErrors.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'أخطاء في الاستيراد:',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                    color: AppColors.error,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: importErrors
                        .take(3)
                        .map(
                          (error) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• $error',
                              style: const TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
                if (importErrors.length > 3)
                  Text(
                    'و ${importErrors.length - 3} أخطاء أخرى...',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'موافق',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 12),
              child: const LogoWidget(size: 32, showText: false),
            ),
            const Text('إدارة الطلاب'),
          ],
        ),
        backgroundColor: AppColors.adminPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.adminPrimary.withValues(alpha: 0.1),
              AppColors.background,
            ],
          ),
        ),
        child: Column(
          children: [
            // Header with actions
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Action buttons row
                  Row(
                    children: [
                      // Add new student button
                      Expanded(
                        child: CustomButton(
                          text: 'إضافة طالب جديد',
                          onPressed: _addNewStudent,
                          backgroundColor: AppColors.success,
                          icon: Icons.person_add,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Import from Excel button
                      Expanded(
                        child: CustomButton(
                          text: 'استيراد من Excel',
                          onPressed: _isLoading ? null : _importFromExcel,
                          backgroundColor: AppColors.info,
                          icon: Icons.upload_file,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Download sample Excel button
                      Expanded(
                        child: CustomButton(
                          text: 'تحميل نموذج Excel',
                          onPressed: _downloadSampleExcel,
                          backgroundColor: AppColors.warning,
                          icon: Icons.download,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Search field
                  CustomTextField(
                    controller: _searchController,
                    labelText: 'البحث عن طالب',
                    hintText: 'ابحث بالاسم، اسم المستخدم، البريد، أو الصف',
                    prefixIcon: Icons.search,
                    onChanged: _filterStudents,
                  ),
                ],
              ),
            ),

            // Students count and pagination info
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Icon(Icons.people, color: AppColors.textSecondary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _currentSearch.isEmpty
                          ? 'إجمالي الطلاب: $_totalStudents'
                          : 'نتائج البحث: ${_filteredStudents.length} من $_totalStudents',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ),
                  if (_totalPages > 1) ...[
                    const SizedBox(width: 16),
                    Text(
                      'صفحة $_currentPage من $_totalPages',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Students list
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredStudents.isEmpty
                  ? _buildEmptyState()
                  : Column(
                      children: [
                        Expanded(child: _buildStudentsList()),
                        if (_totalPages > 1) _buildPaginationControls(),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_outlined, size: 80, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلاب',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة طلاب جدد',
            style: TextStyle(fontSize: 14, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _filteredStudents.length,
      itemBuilder: (context, index) {
        final student = _filteredStudents[index];
        return _buildStudentCard(student);
      },
    );
  }

  Widget _buildPaginationControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        border: Border(
          top: BorderSide(
            color: AppColors.textSecondary.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          ElevatedButton.icon(
            onPressed: _currentPage > 1 ? _goToPreviousPage : null,
            icon: const Icon(Icons.arrow_back_ios, size: 16),
            label: const Text('السابق'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.adminPrimary,
              foregroundColor: Colors.white,
              disabledBackgroundColor: AppColors.textSecondary.withValues(
                alpha: 0.3,
              ),
            ),
          ),

          // Page info
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.adminPrimary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'صفحة $_currentPage من $_totalPages',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.adminPrimary,
              ),
            ),
          ),

          // Next button
          ElevatedButton.icon(
            onPressed: _currentPage < _totalPages ? _goToNextPage : null,
            icon: const Icon(Icons.arrow_forward_ios, size: 16),
            label: const Text('التالي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.adminPrimary,
              foregroundColor: Colors.white,
              disabledBackgroundColor: AppColors.textSecondary.withValues(
                alpha: 0.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
      _loadStudents();
    }
  }

  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
      _loadStudents();
    }
  }

  Widget _buildStudentCard(Student student) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.costaDelSol.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: AppColors.adminPrimary.withValues(alpha: 0.1),
          child: Icon(Icons.person, color: AppColors.adminPrimary),
        ),
        title: Text(
          student.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('اسم المستخدم: ${student.username}'),
            if (student.email != null) Text('البريد: ${student.email}'),
            Text('الصف: ${student.studentClass}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                // TODO: Edit student
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تعديل ${student.fullName}'),
                    backgroundColor: AppColors.info,
                  ),
                );
                break;
              case 'delete':
                // TODO: Delete student
                _showDeleteConfirmation(student);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(Student student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الطالب "${student.fullName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _students.remove(student);
                _filteredStudents.remove(student);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف ${student.fullName}'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }
}
