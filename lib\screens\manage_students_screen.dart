import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/logo_widget.dart';

class ManageStudentsScreen extends StatefulWidget {
  const ManageStudentsScreen({super.key});

  @override
  State<ManageStudentsScreen> createState() => _ManageStudentsScreenState();
}

class _ManageStudentsScreenState extends State<ManageStudentsScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Student> _students = [];
  List<Student> _filteredStudents = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDemoStudents();
    _filteredStudents = _students;
  }

  void _loadDemoStudents() {
    // Demo students data
    _students = [
      Student(
        id: 1,
        username: 'student001',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        studentClass: 'الصف العاشر - أ',
        createdAt: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      ),
      Student(
        id: 2,
        username: 'student002',
        name: 'فاطمة حسن',
        email: '<EMAIL>',
        studentClass: 'الصف التاسع - ب',
        createdAt: DateTime.now().subtract(const Duration(days: 25)).toIso8601String(),
      ),
      Student(
        id: 3,
        username: 'student003',
        name: 'محمد أحمد',
        email: '<EMAIL>',
        studentClass: 'الصف الحادي عشر - أ',
        createdAt: DateTime.now().subtract(const Duration(days: 20)).toIso8601String(),
      ),
      Student(
        id: 4,
        username: 'student004',
        name: 'زينب علي',
        email: '<EMAIL>',
        studentClass: 'الصف العاشر - ب',
        createdAt: DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
      ),
      Student(
        id: 5,
        username: 'student005',
        name: 'علي حسين',
        email: '<EMAIL>',
        studentClass: 'الصف الثاني عشر - أ',
        createdAt: DateTime.now().subtract(const Duration(days: 10)).toIso8601String(),
      ),
      Student(
        id: 6,
        username: 'student006',
        name: 'مريم خالد',
        email: '<EMAIL>',
        studentClass: 'الصف التاسع - أ',
        createdAt: DateTime.now().subtract(const Duration(days: 8)).toIso8601String(),
      ),
      Student(
        id: 7,
        username: 'student007',
        name: 'حسام الدين',
        email: '<EMAIL>',
        studentClass: 'الصف العاشر - أ',
        createdAt: DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
      ),
      Student(
        id: 8,
        username: 'student008',
        name: 'نور الهدى',
        email: '<EMAIL>',
        studentClass: 'الصف الحادي عشر - ب',
        createdAt: DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
      ),
    ];
    _filteredStudents = _students;
  }

  void _filterStudents(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredStudents = _students;
      } else {
        _filteredStudents = _students.where((student) {
          return student.name!.toLowerCase().contains(query.toLowerCase()) ||
                 student.username.toLowerCase().contains(query.toLowerCase()) ||
                 (student.email?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
                 (student.studentClass?.toLowerCase().contains(query.toLowerCase()) ?? false);
        }).toList();
      }
    });
  }

  Future<void> _addNewStudent() async {
    // TODO: Navigate to add student screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('سيتم فتح شاشة إضافة طالب جديد'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  Future<void> _importFromExcel() async {
    try {
      setState(() => _isLoading = true);
      
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
      );

      if (result != null) {
        // TODO: Process Excel file
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم اختيار الملف: ${result.files.single.name}'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اختيار الملف: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 12),
              child: const LogoWidget(size: 32, showText: false),
            ),
            const Text('إدارة الطلاب'),
          ],
        ),
        backgroundColor: AppColors.adminPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.adminPrimary.withValues(alpha: 0.1),
              AppColors.background,
            ],
          ),
        ),
        child: Column(
          children: [
            // Header with actions
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Action buttons row
                  Row(
                    children: [
                      // Add new student button
                      Expanded(
                        child: CustomButton(
                          text: 'إضافة طالب جديد',
                          onPressed: _addNewStudent,
                          backgroundColor: AppColors.success,
                          icon: Icons.person_add,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Import from Excel button
                      Expanded(
                        child: CustomButton(
                          text: 'استيراد من Excel',
                          onPressed: _isLoading ? null : _importFromExcel,
                          backgroundColor: AppColors.info,
                          icon: Icons.upload_file,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Search field
                  CustomTextField(
                    controller: _searchController,
                    labelText: 'البحث عن طالب',
                    hintText: 'ابحث بالاسم، اسم المستخدم، البريد، أو الصف',
                    prefixIcon: Icons.search,
                    onChanged: _filterStudents,
                  ),
                ],
              ),
            ),
            
            // Students count
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Icon(Icons.people, color: AppColors.textSecondary),
                  const SizedBox(width: 8),
                  Text(
                    'عدد الطلاب: ${_filteredStudents.length}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Students list
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredStudents.isEmpty
                      ? _buildEmptyState()
                      : _buildStudentsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.school_outlined,
            size: 80,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلاب',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة طلاب جدد',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _filteredStudents.length,
      itemBuilder: (context, index) {
        final student = _filteredStudents[index];
        return _buildStudentCard(student);
      },
    );
  }

  Widget _buildStudentCard(Student student) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.costaDelSol.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: AppColors.adminPrimary.withValues(alpha: 0.1),
          child: Icon(
            Icons.person,
            color: AppColors.adminPrimary,
          ),
        ),
        title: Text(
          student.name ?? 'غير محدد',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('اسم المستخدم: ${student.username}'),
            if (student.email != null) Text('البريد: ${student.email}'),
            if (student.studentClass != null) Text('الصف: ${student.studentClass}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                // TODO: Edit student
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تعديل ${student.name}'),
                    backgroundColor: AppColors.info,
                  ),
                );
                break;
              case 'delete':
                // TODO: Delete student
                _showDeleteConfirmation(student);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(Student student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الطالب "${student.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _students.remove(student);
                _filteredStudents.remove(student);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف ${student.name}'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
