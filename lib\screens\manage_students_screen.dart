import 'dart:async';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../models/user_model.dart';
import '../services/student_service.dart';
import '../utils/constants.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import 'add_student_screen.dart';
import '../widgets/logo_widget.dart';

class ManageStudentsScreen extends StatefulWidget {
  const ManageStudentsScreen({super.key});

  @override
  State<ManageStudentsScreen> createState() => _ManageStudentsScreenState();
}

class _ManageStudentsScreenState extends State<ManageStudentsScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Student> _students = [];
  List<Student> _filteredStudents = [];
  bool _isLoading = false;

  // Pagination variables
  int _currentPage = 1;
  int _totalPages = 1;
  int _totalStudents = 0;
  final int _perPage = 20;
  String _currentSearch = '';

  // Debounce timer for search
  Timer? _searchTimer;

  @override
  void initState() {
    super.initState();
    _loadStudents();
  }

  // Load students from API with pagination
  Future<void> _loadStudents({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
    }

    setState(() => _isLoading = true);

    try {
      final result = await StudentService.getStudents(
        page: _currentPage,
        perPage: _perPage,
        search: _currentSearch.isEmpty ? null : _currentSearch,
      );

      if (mounted) {
        setState(() {
          _students = result['students'] as List<Student>;
          _filteredStudents = _students; // No local filtering needed
          _totalStudents = result['total'] as int;
          _totalPages = result['last_page'] as int;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الطلاب: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Refresh students list
  Future<void> _refreshStudents() async {
    await _loadStudents(isRefresh: true);
  }

  // Delete student
  Future<void> _deleteStudent(Student student) async {
    try {
      setState(() => _isLoading = true);

      final success = await StudentService.deleteStudent(student.id);

      if (mounted) {
        setState(() => _isLoading = false);

        if (success) {
          // Reload the current page to reflect changes
          await _loadStudents();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف ${student.fullName} بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف ${student.fullName}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الطالب: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Search with debounce for server-side search
  void _filterStudents(String query) {
    // Cancel previous timer
    _searchTimer?.cancel();

    // Start new timer
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      if (_currentSearch != query) {
        setState(() {
          _currentSearch = query;
          _currentPage = 1; // Reset to first page
        });
        _loadStudents();
      }
    });
  }

  Future<void> _addNewStudent() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddStudentScreen()),
    );

    // If student was added successfully, refresh the list
    if (result == true) {
      _refreshStudents();
    }
  }

  Future<void> _importFromExcel() async {
    try {
      setState(() => _isLoading = true);

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
      );

      if (result != null) {
        // TODO: Process Excel file
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم اختيار الملف: ${result.files.single.name}'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اختيار الملف: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 12),
              child: const LogoWidget(size: 32, showText: false),
            ),
            const Text('إدارة الطلاب'),
          ],
        ),
        backgroundColor: AppColors.adminPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.adminPrimary.withValues(alpha: 0.1),
              AppColors.background,
            ],
          ),
        ),
        child: Column(
          children: [
            // Header with actions
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Action buttons row
                  Row(
                    children: [
                      // Add new student button
                      Expanded(
                        child: CustomButton(
                          text: 'إضافة طالب جديد',
                          onPressed: _addNewStudent,
                          backgroundColor: AppColors.success,
                          icon: Icons.person_add,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Import from Excel button
                      Expanded(
                        child: CustomButton(
                          text: 'استيراد من Excel',
                          onPressed: _isLoading ? null : _importFromExcel,
                          backgroundColor: AppColors.info,
                          icon: Icons.upload_file,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Search field
                  CustomTextField(
                    controller: _searchController,
                    labelText: 'البحث عن طالب',
                    hintText: 'ابحث بالاسم، اسم المستخدم، البريد، أو الصف',
                    prefixIcon: Icons.search,
                    onChanged: _filterStudents,
                  ),
                ],
              ),
            ),

            // Students count and pagination info
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Icon(Icons.people, color: AppColors.textSecondary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _currentSearch.isEmpty
                          ? 'إجمالي الطلاب: $_totalStudents'
                          : 'نتائج البحث: ${_filteredStudents.length} من $_totalStudents',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ),
                  if (_totalPages > 1) ...[
                    const SizedBox(width: 16),
                    Text(
                      'صفحة $_currentPage من $_totalPages',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Students list
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredStudents.isEmpty
                  ? _buildEmptyState()
                  : Column(
                      children: [
                        Expanded(child: _buildStudentsList()),
                        if (_totalPages > 1) _buildPaginationControls(),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_outlined, size: 80, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلاب',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة طلاب جدد',
            style: TextStyle(fontSize: 14, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _filteredStudents.length,
      itemBuilder: (context, index) {
        final student = _filteredStudents[index];
        return _buildStudentCard(student);
      },
    );
  }

  Widget _buildPaginationControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        border: Border(
          top: BorderSide(
            color: AppColors.textSecondary.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          ElevatedButton.icon(
            onPressed: _currentPage > 1 ? _goToPreviousPage : null,
            icon: const Icon(Icons.arrow_back_ios, size: 16),
            label: const Text('السابق'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.adminPrimary,
              foregroundColor: Colors.white,
              disabledBackgroundColor: AppColors.textSecondary.withValues(
                alpha: 0.3,
              ),
            ),
          ),

          // Page info
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.adminPrimary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'صفحة $_currentPage من $_totalPages',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.adminPrimary,
              ),
            ),
          ),

          // Next button
          ElevatedButton.icon(
            onPressed: _currentPage < _totalPages ? _goToNextPage : null,
            icon: const Icon(Icons.arrow_forward_ios, size: 16),
            label: const Text('التالي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.adminPrimary,
              foregroundColor: Colors.white,
              disabledBackgroundColor: AppColors.textSecondary.withValues(
                alpha: 0.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
      _loadStudents();
    }
  }

  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
      _loadStudents();
    }
  }

  Widget _buildStudentCard(Student student) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.costaDelSol.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: AppColors.adminPrimary.withValues(alpha: 0.1),
          child: Icon(Icons.person, color: AppColors.adminPrimary),
        ),
        title: Text(
          student.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('اسم المستخدم: ${student.username}'),
            if (student.email != null) Text('البريد: ${student.email}'),
            Text('الصف: ${student.studentClass}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                // TODO: Edit student
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تعديل ${student.fullName}'),
                    backgroundColor: AppColors.info,
                  ),
                );
                break;
              case 'delete':
                // TODO: Delete student
                _showDeleteConfirmation(student);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(Student student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الطالب "${student.fullName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _students.remove(student);
                _filteredStudents.remove(student);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف ${student.fullName}'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }
}
