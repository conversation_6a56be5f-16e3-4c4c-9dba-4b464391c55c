import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_response.dart';
import '../utils/constants.dart';

class ApiService {
  static const Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Admin login
  static Future<AuthResponse?> adminLogin(String email, String password) async {
    try {
      if (kDebugMode) {
        print('🔐 Attempting admin login with Laravel API...');
        print('📤 POST ${ApiConstants.adminLogin}');
        print('📝 Username: $email');
      }

      final response = await http.post(
        Uri.parse(ApiConstants.adminLogin),
        headers: _headers,
        body: jsonEncode({'username': email, 'password': password}),
      );

      if (kDebugMode) {
        print('📥 Login response: ${response.statusCode}');
        print('📄 Response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Save token to SharedPreferences immediately
        if (data['token'] != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(StorageKeys.userToken, data['token']);
          await prefs.setString(StorageKeys.userType, 'admin');

          if (kDebugMode) {
            print(
              '✅ Token saved successfully: ${data['token'].substring(0, 20)}...',
            );
          }
        }

        if (kDebugMode) {
          print('✅ Real Laravel API login successful!');
        }
        return AuthResponse.fromJson(data, 'admin');
      } else {
        if (kDebugMode) {
          print(
            '❌ Laravel API login failed: ${response.statusCode} - ${response.body}',
          );
        }
        return null; // Don't use demo login - force real API
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Admin login error: $e');
      }
      return null; // Don't use demo login - force real API
    }
  }

  // Demo admin login for testing purposes (DISABLED - use real API)
  static AuthResponse? _demoAdminLogin(String username, String password) {
    if (kDebugMode) {
      print('❌ Demo login disabled - Laravel API should be working');
      print('🔧 Check Laravel API connection and admin credentials');
    }
    return null; // Force use of real API
  }

  // Student login
  static Future<AuthResponse?> studentLogin(
    String username,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.studentLogin),
        headers: _headers,
        body: jsonEncode({'username': username, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'student');
      } else {
        print(
          'Student login failed: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Student login error: $e');
      return null;
    }
  }

  // Employee login
  static Future<AuthResponse?> employeeLogin(
    String username,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.employeeLogin),
        headers: _headers,
        body: jsonEncode({'username': username, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'employee');
      } else {
        print(
          'Employee login failed: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Employee login error: $e');
      return null;
    }
  }

  // Get authenticated headers with token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(StorageKeys.userToken);

    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Get user profile (generic method)
  static Future<Map<String, dynamic>?> getUserProfile(String userType) async {
    try {
      // For now, return demo profile data since profile endpoints are not defined in Laravel
      // TODO: Add profile endpoints to Laravel API

      if (kDebugMode) {
        print('Profile endpoint not implemented yet for $userType');
      }

      // Return demo profile data based on separate tables structure
      switch (userType) {
        case 'admin':
          return {
            'admin': {
              'id': 1,
              'name': 'مدير النظام',
              'email': '<EMAIL>',
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            },
          };
        case 'student':
          return {
            'student': {
              'id': 1,
              'name': 'طالب تجريبي',
              'username': 'student123',
              'email': '<EMAIL>',
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            },
          };
        case 'employee':
          return {
            'employee': {
              'id': 1,
              'name': 'موظف تجريبي',
              'username': 'employee123',
              'email': '<EMAIL>',
              'department': 'الإدارة',
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            },
          };
        default:
          return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get profile error: $e');
      }
      return null;
    }
  }

  // Test API connection
  static Future<bool> testConnection() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConstants.healthCheck),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (kDebugMode) {
          print('API Health Check: ${data['message']}');
        }
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('API Connection Test Failed: $e');
      }
      return false;
    }
  }

  // Test database connection
  static Future<bool> testDatabase() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConstants.testDb),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (kDebugMode) {
          print('Database Test: ${data['message']}');
        }
        return data['success'] ?? false;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Database Connection Test Failed: $e');
      }
      return false;
    }
  }
}
