import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class ApiService {
  static const Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Admin login
  static Future<AuthResponse?> adminLogin(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.adminLogin),
        headers: _headers,
        body: jsonEncode({'email': email, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'admin');
      } else {
        print('Admin login failed: ${response.statusCode} - ${response.body}');

        // Fallback to demo login for testing
        return _demoAdminLogin(email, password);
      }
    } catch (e) {
      print('Admin login error: $e');

      // Fallback to demo login for testing when API is not available
      return _demoAdminLogin(email, password);
    }
  }

  // Demo admin login for testing purposes
  static AuthResponse? _demoAdminLogin(String email, String password) {
    // Demo admin credentials
    if (email == '<EMAIL>' && password == 'admin123') {
      return AuthResponse(
        token: 'demo_admin_token_${DateTime.now().millisecondsSinceEpoch}',
        userType: 'admin',
        message: 'تم تسجيل الدخول بنجاح',
        user: User(
          id: 1,
          name: 'مدير النظام',
          email: '<EMAIL>',
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
        ),
      );
    }

    if (kDebugMode) {
      print('Demo admin login failed - Invalid credentials');
      print('Use: <EMAIL> / admin123');
    }
    return null;
  }

  // Student login
  static Future<AuthResponse?> studentLogin(
    String username,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.studentLogin),
        headers: _headers,
        body: jsonEncode({'username': username, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'student');
      } else {
        print(
          'Student login failed: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Student login error: $e');
      return null;
    }
  }

  // Employee login
  static Future<AuthResponse?> employeeLogin(
    String username,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.employeeLogin),
        headers: _headers,
        body: jsonEncode({'username': username, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'employee');
      } else {
        print(
          'Employee login failed: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Employee login error: $e');
      return null;
    }
  }

  // Get authenticated headers with token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(StorageKeys.userToken);

    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Get user profile (generic method)
  static Future<Map<String, dynamic>?> getUserProfile(String userType) async {
    try {
      String endpoint;
      switch (userType) {
        case 'admin':
          endpoint = ApiConstants.adminProfile;
          break;
        case 'student':
          endpoint = ApiConstants.studentProfile;
          break;
        case 'employee':
          endpoint = ApiConstants.employeeProfile;
          break;
        default:
          return null;
      }

      final headers = await _getAuthHeaders();
      final response = await http.get(Uri.parse(endpoint), headers: headers);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        print('Get profile failed: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Get profile error: $e');
      return null;
    }
  }
}
