import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class ApiService {
  static const Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Admin login
  static Future<AuthResponse?> adminLogin(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.adminLogin),
        headers: _headers,
        body: jsonEncode({'email': email, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'admin');
      } else {
        print('Admin login failed: ${response.statusCode} - ${response.body}');

        // Fallback to demo login for testing
        return _demoAdminLogin(email, password);
      }
    } catch (e) {
      print('Admin login error: $e');

      // Fallback to demo login for testing when API is not available
      return _demoAdminLogin(email, password);
    }
  }

  // Demo admin login for testing purposes
  static AuthResponse? _demoAdminLogin(String email, String password) {
    // Demo admin credentials
    if (email == '<EMAIL>' && password == 'admin123') {
      return AuthResponse(
        token: 'demo_admin_token_${DateTime.now().millisecondsSinceEpoch}',
        userType: 'admin',
        message: 'تم تسجيل الدخول بنجاح',
        user: User(
          id: 1,
          name: 'مدير النظام',
          email: '<EMAIL>',
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
        ),
      );
    }

    if (kDebugMode) {
      print('Demo admin login failed - Invalid credentials');
      print('Use: <EMAIL> / admin123');
    }
    return null;
  }

  // Student login
  static Future<AuthResponse?> studentLogin(
    String username,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.studentLogin),
        headers: _headers,
        body: jsonEncode({'username': username, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'student');
      } else {
        print(
          'Student login failed: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Student login error: $e');
      return null;
    }
  }

  // Employee login
  static Future<AuthResponse?> employeeLogin(
    String username,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.employeeLogin),
        headers: _headers,
        body: jsonEncode({'username': username, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AuthResponse.fromJson(data, 'employee');
      } else {
        print(
          'Employee login failed: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Employee login error: $e');
      return null;
    }
  }

  // Get authenticated headers with token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(StorageKeys.userToken);

    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Get user profile (generic method)
  static Future<Map<String, dynamic>?> getUserProfile(String userType) async {
    try {
      // For now, return demo profile data since profile endpoints are not defined in Laravel
      // TODO: Add profile endpoints to Laravel API

      if (kDebugMode) {
        print('Profile endpoint not implemented yet for $userType');
      }

      // Return demo profile data
      switch (userType) {
        case 'admin':
          return {
            'id': 1,
            'name': 'مدير النظام',
            'email': '<EMAIL>',
            'role': 'admin',
          };
        case 'student':
          return {
            'id': 1,
            'name': 'طالب تجريبي',
            'username': 'student123',
            'email': '<EMAIL>',
          };
        case 'employee':
          return {
            'id': 1,
            'name': 'موظف تجريبي',
            'username': 'employee123',
            'email': '<EMAIL>',
            'department': 'الإدارة',
          };
        default:
          return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get profile error: $e');
      }
      return null;
    }
  }

  // Test API connection
  static Future<bool> testConnection() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConstants.healthCheck),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (kDebugMode) {
          print('API Health Check: ${data['message']}');
        }
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('API Connection Test Failed: $e');
      }
      return false;
    }
  }

  // Test database connection
  static Future<bool> testDatabase() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConstants.testDb),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (kDebugMode) {
          print('Database Test: ${data['message']}');
        }
        return data['success'] ?? false;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Database Connection Test Failed: $e');
      }
      return false;
    }
  }
}
