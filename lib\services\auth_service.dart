import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_response.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';
import 'api_service.dart';

class AuthService extends ChangeNotifier {
  bool _isAuthenticated = false;
  String? _userType;
  dynamic _currentUser;
  String? _token;
  bool _isLoading = false;

  // Getters
  bool get isAuthenticated => _isAuthenticated;
  String? get userType => _userType;
  dynamic get currentUser => _currentUser;
  String? get token => _token;
  bool get isLoading => _isLoading;

  // Initialize auth service - check if user is already logged in
  Future<void> init() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      _token = prefs.getString(StorageKeys.userToken);
      _userType = prefs.getString(StorageKeys.userType);

      if (_token != null && _userType != null) {
        final userDataString = prefs.getString(StorageKeys.userData);
        if (userDataString != null) {
          final userData = jsonDecode(userDataString);

          switch (_userType) {
            case 'admin':
              _currentUser = Admin.fromJson(userData);
              break;
            case 'student':
              _currentUser = Student.fromJson(userData);
              break;
            case 'employee':
              _currentUser = Employee.fromJson(userData);
              break;
          }

          _isAuthenticated = true;
        }
      }
    } catch (e) {
      print('Auth init error: $e');
      await logout();
    }

    _isLoading = false;
    notifyListeners();
  }

  // Admin login
  Future<bool> loginAdmin(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await ApiService.adminLogin(email, password);

      if (response != null) {
        await _saveAuthData(response);
        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      print('Admin login error: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  // Student login
  Future<bool> loginStudent(String username, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await ApiService.studentLogin(username, password);

      if (response != null) {
        await _saveAuthData(response);
        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      print('Student login error: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  // Employee login
  Future<bool> loginEmployee(String username, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await ApiService.employeeLogin(username, password);

      if (response != null) {
        await _saveAuthData(response);
        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      print('Employee login error: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  // Save authentication data
  Future<void> _saveAuthData(AuthResponse response) async {
    final prefs = await SharedPreferences.getInstance();

    _token = response.token;
    _userType = response.userType;
    _isAuthenticated = true;

    if (kDebugMode) {
      print('💾 Saving auth data:');
      print('   Token: ${response.token.substring(0, 20)}...');
      print('   User Type: ${response.userType}');
      print('   User: ${response.user}');
    }

    await prefs.setString(StorageKeys.userToken, response.token);
    await prefs.setString(StorageKeys.userType, response.userType);

    // Convert user data based on type
    dynamic userObject;
    switch (response.userType) {
      case 'admin':
        userObject = Admin.fromJson(response.user);
        break;
      case 'student':
        userObject = Student.fromJson(response.user);
        break;
      case 'employee':
        userObject = Employee.fromJson(response.user);
        break;
      default:
        userObject = response.user;
    }

    _currentUser = userObject;

    // Convert user object to JSON string for storage
    String userDataString;
    if (userObject is Admin) {
      userDataString = jsonEncode(userObject.toJson());
    } else if (userObject is Student) {
      userDataString = jsonEncode(userObject.toJson());
    } else if (userObject is Employee) {
      userDataString = jsonEncode(userObject.toJson());
    } else if (userObject is User) {
      userDataString = jsonEncode(userObject.toJson());
    } else {
      userDataString = jsonEncode(userObject);
    }

    await prefs.setString(StorageKeys.userData, userDataString);
  }

  // Logout
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove(StorageKeys.userToken);
    await prefs.remove(StorageKeys.userType);
    await prefs.remove(StorageKeys.userData);

    _isAuthenticated = false;
    _userType = null;
    _currentUser = null;
    _token = null;

    notifyListeners();
  }

  // Get user display name
  String getUserDisplayName() {
    if (_currentUser == null) return 'User';

    if (_currentUser is Admin) {
      return (_currentUser as Admin).name ?? (_currentUser as Admin).email;
    } else if (_currentUser is Student) {
      return (_currentUser as Student).name ??
          (_currentUser as Student).username;
    } else if (_currentUser is Employee) {
      return (_currentUser as Employee).name ??
          (_currentUser as Employee).username;
    } else if (_currentUser is User) {
      return (_currentUser as User).name ?? (_currentUser as User).email;
    }

    return 'User';
  }

  // Static method to get stored token
  static Future<String?> getStoredToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(StorageKeys.userToken);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting stored token: $e');
      }
      return null;
    }
  }
}
