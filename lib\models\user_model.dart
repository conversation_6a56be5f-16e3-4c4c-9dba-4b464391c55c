// Admin model for admins table
class Admin {
  final int id;
  final String email;
  final String? name;
  final String? createdAt;
  final String? updatedAt;

  Admin({
    required this.id,
    required this.email,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  factory Admin.fromJson(Map<String, dynamic> json) {
    return Admin(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      name: json['name'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

// Keep User class for backward compatibility (can be removed later)
class User {
  final int id;
  final String email;
  final String? name;
  final String? createdAt;
  final String? updatedAt;

  User({
    required this.id,
    required this.email,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      name: json['name'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class Student {
  final int id;
  final String username; // NOT NULL
  final String fullName; // NOT NULL
  final String nationality; // NOT NULL
  final String? phone; // NULLABLE
  final String specialization; // NOT NULL
  final String section; // NOT NULL
  final String studentClass; // class field - NOT NULL
  final String level; // NOT NULL
  final String? result; // NULLABLE
  final String password; // NOT NULL, DEFAULT 'stu123'
  final bool isActive; // NOT NULL, DEFAULT 1
  final String? rememberToken; // NULLABLE
  final String? createdAt; // NULLABLE
  final String? updatedAt; // NULLABLE

  Student({
    required this.id,
    required this.username,
    required this.fullName,
    required this.nationality,
    this.phone,
    required this.specialization,
    required this.section,
    required this.studentClass,
    required this.level,
    this.result,
    this.password = 'stu123',
    this.isActive = true,
    this.rememberToken,
    this.createdAt,
    this.updatedAt,
  });

  // Getter for display name (backward compatibility)
  String get name => fullName;

  // Getter for email (backward compatibility - students don't have email)
  String? get email => null;

  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      fullName: json['full_name'] ?? '',
      nationality: json['nationality'] ?? '',
      phone: json['phone'],
      specialization: json['specialization'] ?? '',
      section: json['section'] ?? '',
      studentClass: json['class'] ?? '',
      level: json['level'] ?? '',
      result: json['result'],
      password: json['password'] ?? 'stu123',
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      rememberToken: json['remember_token'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'full_name': fullName,
      'nationality': nationality,
      'phone': phone,
      'specialization': specialization,
      'section': section,
      'class': studentClass,
      'level': level,
      'result': result,
      'password': password,
      'is_active': isActive ? 1 : 0,
      'remember_token': rememberToken,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class Employee {
  final int id;
  final String username;
  final String? name;
  final String? email;
  final String? department;
  final String? createdAt;
  final String? updatedAt;

  Employee({
    required this.id,
    required this.username,
    this.name,
    this.email,
    this.department,
    this.createdAt,
    this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      name: json['name'],
      email: json['email'],
      department: json['department'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'email': email,
      'department': department,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class AuthResponse {
  final String token;
  final dynamic user; // Can be Admin, Student, or Employee
  final String userType;
  final String message;

  AuthResponse({
    required this.token,
    required this.user,
    required this.userType,
    required this.message,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json, String userType) {
    // Handle Laravel API response structure
    Map<String, dynamic> responseData = json;

    // If response has 'data' field (Laravel API structure)
    if (json.containsKey('data') && json['data'] is Map<String, dynamic>) {
      responseData = json['data'] as Map<String, dynamic>;
    }

    dynamic userData;

    switch (userType) {
      case 'admin':
        userData = Admin.fromJson(
          responseData['admin'] ??
          responseData['user'] ??
          json['admin'] ??
          json['user'] ??
          {}
        );
        break;
      case 'student':
        userData = Student.fromJson(
          responseData['student'] ??
          responseData['user'] ??
          json['student'] ??
          json['user'] ??
          {}
        );
        break;
      case 'employee':
        userData = Employee.fromJson(
          responseData['employee'] ??
          responseData['user'] ??
          json['employee'] ??
          json['user'] ??
          {}
        );
        break;
      default:
        userData = responseData['user'] ??
                   json['user'] ??
                   responseData['admin'] ??
                   json['admin'] ??
                   responseData['student'] ??
                   json['student'] ??
                   responseData['employee'] ??
                   json['employee'];
    }

    return AuthResponse(
      token: responseData['token'] ?? json['token'] ?? '',
      user: userData,
      userType: userType,
      message: json['message'] ?? responseData['message'] ?? '',
    );
  }
}
