// Admin model for admins table
class Admin {
  final int id;
  final String email;
  final String? name;
  final String? createdAt;
  final String? updatedAt;

  Admin({
    required this.id,
    required this.email,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  factory Admin.fromJson(Map<String, dynamic> json) {
    return Admin(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      name: json['name'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

// Keep User class for backward compatibility (can be removed later)
class User {
  final int id;
  final String email;
  final String? name;
  final String? createdAt;
  final String? updatedAt;

  User({
    required this.id,
    required this.email,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      name: json['name'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class Student {
  final int id;
  final String username;
  final String? name;
  final String? email;
  final String? studentClass;
  final String? createdAt;
  final String? updatedAt;

  Student({
    required this.id,
    required this.username,
    this.name,
    this.email,
    this.studentClass,
    this.createdAt,
    this.updatedAt,
  });

  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      name: json['name'],
      email: json['email'],
      studentClass: json['student_class'] ?? json['class'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'email': email,
      'student_class': studentClass,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class Employee {
  final int id;
  final String username;
  final String? name;
  final String? email;
  final String? department;
  final String? createdAt;
  final String? updatedAt;

  Employee({
    required this.id,
    required this.username,
    this.name,
    this.email,
    this.department,
    this.createdAt,
    this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      name: json['name'],
      email: json['email'],
      department: json['department'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'email': email,
      'department': department,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class AuthResponse {
  final String token;
  final dynamic user; // Can be Admin, Student, or Employee
  final String userType;
  final String message;

  AuthResponse({
    required this.token,
    required this.user,
    required this.userType,
    required this.message,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json, String userType) {
    dynamic userData;

    switch (userType) {
      case 'admin':
        userData = Admin.fromJson(json['admin'] ?? json['user'] ?? {});
        break;
      case 'student':
        userData = Student.fromJson(json['student'] ?? json['user'] ?? {});
        break;
      case 'employee':
        userData = Employee.fromJson(json['employee'] ?? json['user'] ?? {});
        break;
      default:
        userData =
            json['user'] ??
            json['admin'] ??
            json['student'] ??
            json['employee'];
    }

    return AuthResponse(
      token: json['token'] ?? '',
      user: userData,
      userType: userType,
      message: json['message'] ?? '',
    );
  }
}
