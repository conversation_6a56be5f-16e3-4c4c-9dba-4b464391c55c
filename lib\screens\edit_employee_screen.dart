import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/employee.dart';
import '../services/employee_service.dart';
import '../utils/constants.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';
import '../widgets/logo_widget.dart';

class EditEmployeeScreen extends StatefulWidget {
  final Employee employee;

  const EditEmployeeScreen({super.key, required this.employee});

  @override
  State<EditEmployeeScreen> createState() => _EditEmployeeScreenState();
}

class _EditEmployeeScreenState extends State<EditEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _automaticNumberController = TextEditingController();
  final _financialNumberController = TextEditingController();
  final _stateCooperativeNumberController = TextEditingController();
  final _bankAccountNumberController = TextEditingController();

  String? _selectedContractType;
  String? _selectedEmployeeType;
  String? _selectedJobStatus;
  bool _isLoading = false;

  // Form data options
  final List<String> _contractTypes = [
    'تعاقد عادي',
    'دوام كامل',
    'دوام جزئي',
    'مؤقت',
    'استشاري',
  ];

  final List<String> _employeeTypes = [
    'Teacher',
    'Admin',
    'Technical',
    'Worker',
    'Supervisor',
  ];

  final List<String> _jobStatuses = [
    'Active',
    'Inactive',
    'On Leave',
    'Terminated',
  ];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    // Initialize form with employee data
    _fullNameController.text = widget.employee.fullName;
    _phoneController.text = widget.employee.phone ?? '';
    _automaticNumberController.text = widget.employee.automaticNumber ?? '';
    _financialNumberController.text = widget.employee.financialNumber ?? '';
    _stateCooperativeNumberController.text =
        widget.employee.stateCooperativeNumber ?? '';
    _bankAccountNumberController.text = widget.employee.bankAccountNumber ?? '';

    _selectedContractType = widget.employee.contractType;
    _selectedEmployeeType = widget.employee.employeeType;
    _selectedJobStatus = widget.employee.jobStatus;
  }

  Future<void> _updateEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      if (kDebugMode) {
        print('📝 Updating employee: ${widget.employee.id}');
      }

      final employeeData = {
        'full_name': _fullNameController.text.trim(),
        'phone': _phoneController.text.trim(),
        'contract_type': _selectedContractType ?? '',
        'employee_type': _selectedEmployeeType ?? '',
        'job_status': _selectedJobStatus ?? '',
        'automatic_number': _automaticNumberController.text.trim(),
        'financial_number': _financialNumberController.text.trim(),
        'state_cooperative_number': _stateCooperativeNumberController.text
            .trim(),
        'bank_account_number': _bankAccountNumberController.text.trim(),
      };

      if (kDebugMode) {
        print('📤 Sending update data: $employeeData');
      }

      final updatedEmployee = await EmployeeService.updateEmployee(
        widget.employee.id,
        employeeData,
      );

      if (updatedEmployee != null) {
        // Clear cache to refresh data
        EmployeeService.clearCache();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تحديث بيانات ${updatedEmployee.fullName} بنجاح',
              ),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop(updatedEmployee); // Return updated employee
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('فشل في تحديث بيانات الموظف'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Update employee error: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الموظف: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    String? Function(String?)? validator,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8, right: 4),
          child: Row(
            children: [
              Icon(
                Icons.arrow_drop_down_circle,
                size: 18,
                color: const Color(0xFF5D6E35),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: const TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
              if (isRequired) ...[
                const SizedBox(width: 4),
                const Text(
                  '*',
                  style: TextStyle(
                    color: Color(0xFFE74C3C),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF5D6E35).withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            decoration: InputDecoration(
              hintText: 'اختر $label',
              hintStyle: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 14,
                color: Colors.grey[500],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Color(0xFF5D6E35),
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            items: items.map((item) {
              return DropdownMenuItem(
                value: item,
                child: Text(
                  item,
                  style: const TextStyle(fontFamily: 'Cairo', fontSize: 14),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            validator: validator,
            style: const TextStyle(
              fontFamily: 'Cairo',
              fontSize: 14,
              color: Color(0xFF2C3E50),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.adminPrimary,
        foregroundColor: Colors.white,
        title: Text(
          'تعديل الموظف: ${widget.employee.fullName}',
          style: const TextStyle(
            fontFamily: 'Cairo',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Logo
            const LogoWidget(),
            const SizedBox(height: 30),

            // Form Card
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.cardBackground,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.costaDelSol.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      'تعديل بيانات الموظف',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.adminPrimary,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Username field (read-only)
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.person, color: Colors.grey[600], size: 22),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'اسم المستخدم',
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.employee.username,
                                  style: const TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 16,
                                    color: Color(0xFF2C3E50),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              'غير قابل للتعديل',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 10,
                                color: Colors.grey[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Full name field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8, right: 4),
                          child: Row(
                            children: [
                              Icon(
                                Icons.person,
                                size: 18,
                                color: const Color(0xFF5D6E35),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'الاسم الكامل',
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                '*',
                                style: TextStyle(
                                  color: Color(0xFFE74C3C),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(
                                  0xFF5D6E35,
                                ).withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: CustomTextField(
                            controller: _fullNameController,
                            labelText: '',
                            hintText: 'أدخل الاسم الكامل للموظف',
                            prefixIcon: null,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الاسم الكامل مطلوب';
                              }
                              if (value.length < 2) {
                                return 'الاسم يجب أن يكون حرفين على الأقل';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Phone field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8, right: 4),
                          child: Row(
                            children: [
                              Icon(
                                Icons.phone_android,
                                size: 18,
                                color: Colors.blue,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'رقم الهاتف',
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              const SizedBox(width: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Text(
                                  'اختياري',
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 10,
                                    color: Colors.blue,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.blue.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: CustomTextField(
                            controller: _phoneController,
                            labelText: '',
                            hintText: 'أدخل رقم الهاتف (مثال: 70123456)',
                            prefixIcon: null,
                            keyboardType: TextInputType.phone,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                if (value.length < 8) {
                                  return 'رقم الهاتف يجب أن يكون 8 أرقام على الأقل';
                                }
                                if (!RegExp(r'^[0-9+\-\s]+$').hasMatch(value)) {
                                  return 'رقم الهاتف يجب أن يحتوي على أرقام فقط';
                                }
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Contract Type dropdown
                    _buildDropdownField(
                      label: 'نوع العقد',
                      value: _selectedContractType,
                      items: _contractTypes,
                      onChanged: (value) =>
                          setState(() => _selectedContractType = value),
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'نوع العقد مطلوب';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),

                    // Employee Type dropdown
                    _buildDropdownField(
                      label: 'نوع الموظف',
                      value: _selectedEmployeeType,
                      items: _employeeTypes,
                      onChanged: (value) =>
                          setState(() => _selectedEmployeeType = value),
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'نوع الموظف مطلوب';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),

                    // Job Status dropdown
                    _buildDropdownField(
                      label: 'حالة الوظيفة',
                      value: _selectedJobStatus,
                      items: _jobStatuses,
                      onChanged: (value) =>
                          setState(() => _selectedJobStatus = value),
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'حالة الوظيفة مطلوبة';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),

                    // Automatic Number field
                    CustomTextField(
                      controller: _automaticNumberController,
                      labelText: 'الرقم الآلي',
                      hintText: 'أدخل الرقم الآلي (اختياري)',
                      prefixIcon: Icons.numbers,
                    ),
                    const SizedBox(height: 16),

                    // Financial Number field
                    CustomTextField(
                      controller: _financialNumberController,
                      labelText: 'الرقم المالي',
                      hintText: 'أدخل الرقم المالي (اختياري)',
                      prefixIcon: Icons.account_balance,
                    ),
                    const SizedBox(height: 16),

                    // State Cooperative Number field
                    CustomTextField(
                      controller: _stateCooperativeNumberController,
                      labelText: 'رقم التعاونية الحكومية',
                      hintText: 'أدخل رقم التعاونية الحكومية (اختياري)',
                      prefixIcon: Icons.business,
                    ),
                    const SizedBox(height: 16),

                    // Bank Account Number field
                    CustomTextField(
                      controller: _bankAccountNumberController,
                      labelText: 'رقم الحساب المصرفي',
                      hintText: 'أدخل رقم الحساب المصرفي (اختياري)',
                      prefixIcon: Icons.account_balance_wallet,
                    ),
                    const SizedBox(height: 32),

                    // Update button
                    SizedBox(
                      width: double.infinity,
                      child: CustomButton(
                        text: _isLoading ? 'جاري التحديث...' : 'تحديث الموظف',
                        onPressed: _isLoading ? null : _updateEmployee,
                        backgroundColor: AppColors.adminPrimary,
                        textColor: Colors.white,
                        icon: _isLoading ? null : Icons.update,
                        height: 50,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _automaticNumberController.dispose();
    _financialNumberController.dispose();
    _stateCooperativeNumberController.dispose();
    _bankAccountNumberController.dispose();
    super.dispose();
  }
}
