import 'package:flutter/material.dart';
import '../models/employee.dart';
import '../services/employee_service.dart';

class EditEmployeeScreen extends StatefulWidget {
  final Employee employee;

  const EditEmployeeScreen({Key? key, required this.employee})
    : super(key: key);

  @override
  State<EditEmployeeScreen> createState() => _EditEmployeeScreenState();
}

class _EditEmployeeScreenState extends State<EditEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _automaticNumberController = TextEditingController();
  final _financialNumberController = TextEditingController();
  final _stateCooperativeNumberController = TextEditingController();
  final _bankAccountNumberController = TextEditingController();

  String? _selectedContractType;
  String? _selectedEmployeeType;
  String? _selectedJobStatus;

  bool _isLoading = false;

  // Dropdown options
  final List<String> _contractTypes = [
    'تعاقد عادي',
    'تعاقد مؤقت',
    'دوام كامل',
    'دوام جزئي',
  ];

  final List<String> _employeeTypes = [
    'مدرس',
    'إداري',
    'فني',
    'عامل',
    'غير موظف',
  ];

  final List<String> _jobStatuses = ['نشط', 'غير نشط', 'معلق', 'مفصول'];

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    _fullNameController.text = widget.employee.fullName;
    _phoneController.text = widget.employee.phone ?? '';
    _automaticNumberController.text = widget.employee.automaticNumber ?? '';
    _financialNumberController.text = widget.employee.financialNumber ?? '';
    _stateCooperativeNumberController.text =
        widget.employee.stateCooperativeNumber ?? '';
    _bankAccountNumberController.text = widget.employee.bankAccountNumber ?? '';

    _selectedContractType = widget.employee.contractType;
    _selectedEmployeeType = widget.employee.employeeType;
    _selectedJobStatus = widget.employee.jobStatus;
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _automaticNumberController.dispose();
    _financialNumberController.dispose();
    _stateCooperativeNumberController.dispose();
    _bankAccountNumberController.dispose();
    super.dispose();
  }

  Future<void> _updateEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final employeeData = {
        'full_name': _fullNameController.text.trim(),
        'phone': _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        'contract_type': _selectedContractType,
        'employee_type': _selectedEmployeeType,
        'job_status': _selectedJobStatus,
        'automatic_number': _automaticNumberController.text.trim().isEmpty
            ? null
            : _automaticNumberController.text.trim(),
        'financial_number': _financialNumberController.text.trim().isEmpty
            ? null
            : _financialNumberController.text.trim(),
        'state_cooperative_number':
            _stateCooperativeNumberController.text.trim().isEmpty
            ? null
            : _stateCooperativeNumberController.text.trim(),
        'bank_account_number': _bankAccountNumberController.text.trim().isEmpty
            ? null
            : _bankAccountNumberController.text.trim(),
      };

      final updatedEmployee = await EmployeeService.updateEmployee(
        widget.employee.id,
        employeeData,
      );

      if (updatedEmployee != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث بيانات الموظف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في تحديث بيانات الموظف'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? Function(String?)? validator,
    TextInputType keyboardType = TextInputType.text,
    bool isRequired = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        style: const TextStyle(fontFamily: 'Cairo', fontSize: 14),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            fontFamily: 'Cairo',
            fontSize: 14,
            color: Color(0xFF5d6e35),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF5d6e35)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF5d6e35), width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    String? Function(String?)? validator,
    bool isRequired = false,
  }) {
    // Create a safe list with unique items and ensure current value is included
    Set<String> uniqueItems = Set.from(items);
    String? safeValue = value;

    // Handle empty or null values
    if (value == null || value.isEmpty) {
      safeValue = null;
    } else {
      // Always add the current value to ensure it's in the list
      uniqueItems.add(value);
    }

    // Convert back to sorted list
    List<String> safeItems = uniqueItems.toList()..sort();

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: DropdownButtonFormField<String>(
        value: safeItems.contains(safeValue) ? safeValue : null,
        items: safeItems.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: const TextStyle(fontFamily: 'Cairo', fontSize: 14),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        validator: validator,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            fontFamily: 'Cairo',
            fontSize: 14,
            color: Color(0xFF5d6e35),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF5d6e35)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF5d6e35), width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          color: Colors.black,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf4ecdc),
      appBar: AppBar(
        title: const Text(
          'تعديل بيانات الموظف',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF5d6e35),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFF5d6e35)),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildTextField(
                      controller: _fullNameController,
                      label: 'الاسم الكامل *',
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'الاسم الكامل مطلوب';
                        }
                        return null;
                      },
                    ),
                    _buildTextField(
                      controller: _phoneController,
                      label: 'رقم الهاتف',
                      keyboardType: TextInputType.phone,
                    ),
                    _buildDropdownField(
                      label: 'نوع العقد *',
                      value: _selectedContractType,
                      items: _contractTypes,
                      isRequired: true,
                      onChanged: (value) {
                        setState(() {
                          _selectedContractType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'نوع العقد مطلوب';
                        }
                        return null;
                      },
                    ),
                    _buildDropdownField(
                      label: 'نوع الموظف *',
                      value: _selectedEmployeeType,
                      items: _employeeTypes,
                      isRequired: true,
                      onChanged: (value) {
                        setState(() {
                          _selectedEmployeeType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'نوع الموظف مطلوب';
                        }
                        return null;
                      },
                    ),
                    _buildDropdownField(
                      label: 'حالة الوظيفة *',
                      value: _selectedJobStatus,
                      items: _jobStatuses,
                      isRequired: true,
                      onChanged: (value) {
                        setState(() {
                          _selectedJobStatus = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'حالة الوظيفة مطلوبة';
                        }
                        return null;
                      },
                    ),
                    _buildTextField(
                      controller: _automaticNumberController,
                      label: 'الرقم الآلي',
                      keyboardType: TextInputType.number,
                    ),
                    _buildTextField(
                      controller: _financialNumberController,
                      label: 'الرقم المالي',
                      keyboardType: TextInputType.number,
                    ),
                    _buildTextField(
                      controller: _stateCooperativeNumberController,
                      label: 'رقم التعاونية',
                      keyboardType: TextInputType.number,
                    ),
                    _buildTextField(
                      controller: _bankAccountNumberController,
                      label: 'رقم الحساب المصرفي',
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _updateEmployee,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF5d6e35),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Text(
                              'تحديث البيانات',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
