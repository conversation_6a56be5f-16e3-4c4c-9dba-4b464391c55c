import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/employee.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

class EmployeeService {
  static const String baseUrl = '${ApiConstants.baseUrl}/employees';
  static List<Employee>? _cachedEmployees;
  static DateTime? _lastCacheTime;
  static const Duration cacheTimeout = Duration(minutes: 5);

  // Clear cache
  static void clearCache() {
    _cachedEmployees = null;
    _lastCacheTime = null;
  }

  // Check if cache is valid
  static bool get _isCacheValid {
    if (_cachedEmployees == null || _lastCacheTime == null) return false;
    return DateTime.now().difference(_lastCacheTime!) < cacheTimeout;
  }

  // Get all employees
  static Future<List<Employee>> getEmployees({
    String? contractType,
    String? employeeType,
    String? jobStatus,
    String? search,
    int? page,
    int? perPage,
  }) async {
    try {
      // Use cache if valid and no filters
      if (_isCacheValid &&
          contractType == null &&
          employeeType == null &&
          jobStatus == null &&
          search == null) {
        if (kDebugMode) {
          print('📋 Using cached employees data');
        }
        return _cachedEmployees!;
      }

      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Build query parameters
      final queryParams = <String, String>{};
      if (contractType != null) queryParams['contract_type'] = contractType;
      if (employeeType != null) queryParams['employee_type'] = employeeType;
      if (jobStatus != null) queryParams['job_status'] = jobStatus;
      if (search != null) queryParams['search'] = search;
      if (page != null) queryParams['page'] = page.toString();
      if (perPage != null) queryParams['per_page'] = perPage.toString();

      final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);

      if (kDebugMode) {
        print('🔑 Using token for employees API: ${token.substring(0, 20)}...');
        print('📤 GET $uri');
      }

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print(
          '📥 Get employees response: ${response.statusCode} - ${response.body.substring(0, 200)}...',
        );
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> employeesJson = data['data'] ?? data;
        final employees = employeesJson
            .map((json) => Employee.fromJson(json))
            .toList();

        // Cache only if no filters applied
        if (contractType == null &&
            employeeType == null &&
            jobStatus == null &&
            search == null) {
          _cachedEmployees = employees;
          _lastCacheTime = DateTime.now();
          if (kDebugMode) {
            print('💾 Cached ${employees.length} employees');
          }
        }

        return employees;
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized - Please login again');
      } else {
        throw Exception(
          'Failed to get employees: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employees: $e');
      }

      // Return cached data if available and error is network related
      if (_cachedEmployees != null &&
          e.toString().contains('Failed host lookup')) {
        if (kDebugMode) {
          print('📱 Using cached data due to network error');
        }
        return _cachedEmployees!;
      }

      // Return demo data for development
      if (kDebugMode) {
        print('Using demo employees data...');
        return _generateDemoEmployees();
      }

      rethrow;
    }
  }

  // Get single employee
  static Future<Employee?> getEmployee(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Employee.fromJson(data['data'] ?? data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to get employee: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employee: $e');
      }
      rethrow;
    }
  }

  // Create employee
  static Future<Employee?> createEmployee(
    Map<String, dynamic> employeeData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Creating employee: $employeeData');
      }

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(employeeData),
      );

      if (kDebugMode) {
        print(
          '📥 Create employee response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = json.decode(response.body);
        clearCache(); // Clear cache after creating
        return Employee.fromJson(data['data'] ?? data);
      } else {
        throw Exception(
          'Failed to create employee: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating employee: $e');
      }
      rethrow;
    }
  }

  // Update employee
  static Future<Employee?> updateEmployee(
    int id,
    Map<String, dynamic> employeeData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Updating employee $id: $employeeData');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(employeeData),
      );

      if (kDebugMode) {
        print(
          '📥 Update employee response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        clearCache(); // Clear cache after updating
        return Employee.fromJson(data['data'] ?? data);
      } else {
        throw Exception(
          'Failed to update employee: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating employee: $e');
      }
      rethrow;
    }
  }

  // Delete employee
  static Future<bool> deleteEmployee(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.delete(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        clearCache(); // Clear cache after deleting
        return true;
      } else {
        throw Exception('Failed to delete employee: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting employee: $e');
      }
      rethrow;
    }
  }

  // Get employee options (for dropdowns)
  static Future<Map<String, List<String>>> getEmployeeOptions() async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final contractTypesResponse = await http.get(
        Uri.parse('$baseUrl/options/contract-types'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      final employeeTypesResponse = await http.get(
        Uri.parse('$baseUrl/options/employee-types'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      final jobStatusesResponse = await http.get(
        Uri.parse('$baseUrl/options/job-statuses'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      return {
        'contractTypes': contractTypesResponse.statusCode == 200
            ? List<String>.from(json.decode(contractTypesResponse.body)['data'])
            : ['دوام كامل', 'دوام جزئي', 'مؤقت', 'استشاري'],
        'employeeTypes': employeeTypesResponse.statusCode == 200
            ? List<String>.from(json.decode(employeeTypesResponse.body)['data'])
            : ['مدرس', 'إداري', 'فني', 'عامل', 'مشرف'],
        'jobStatuses': jobStatusesResponse.statusCode == 200
            ? List<String>.from(json.decode(jobStatusesResponse.body)['data'])
            : ['نشط', 'غير نشط', 'إجازة', 'مفصول'],
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employee options: $e');
      }
      // Return default options
      return {
        'contractTypes': ['دوام كامل', 'دوام جزئي', 'مؤقت', 'استشاري'],
        'employeeTypes': ['مدرس', 'إداري', 'فني', 'عامل', 'مشرف'],
        'jobStatuses': ['نشط', 'غير نشط', 'إجازة', 'مفصول'],
      };
    }
  }

  // Generate demo employees for development
  static List<Employee> _generateDemoEmployees() {
    final demoEmployees = <Employee>[];
    final contractTypes = ['دوام كامل', 'دوام جزئي', 'مؤقت', 'استشاري'];
    final employeeTypes = ['مدرس', 'إداري', 'فني', 'عامل', 'مشرف'];
    final jobStatuses = ['نشط', 'غير نشط', 'إجازة'];
    final names = [
      'أحمد محمد علي',
      'فاطمة حسن أحمد',
      'محمد عبدالله حسن',
      'نور الدين محمد',
      'سارة علي حسن',
      'عبدالرحمن أحمد',
      'ليلى محمد علي',
      'يوسف حسن محمد',
      'زينب أحمد علي',
      'عمر محمد حسن',
    ];

    for (int i = 0; i < names.length; i++) {
      demoEmployees.add(
        Employee(
          id: i + 1,
          fullName: names[i],
          phone: '7${(1000000 + i * 111111).toString().substring(0, 7)}',
          contractType: contractTypes[i % contractTypes.length],
          employeeType: employeeTypes[i % employeeTypes.length],
          jobStatus: jobStatuses[i % jobStatuses.length],
          username: 'emp${(i + 1).toString().padLeft(3, '0')}',
          automaticNumber: 'AUTO${(i + 1).toString().padLeft(3, '0')}',
          financialNumber: 'FIN${(i + 1).toString().padLeft(3, '0')}',
          createdAt: DateTime.now().subtract(Duration(days: i * 10)),
          updatedAt: DateTime.now().subtract(Duration(days: i * 5)),
        ),
      );
    }

    if (kDebugMode) {
      print('📊 Generated ${demoEmployees.length} demo employees');
    }

    return demoEmployees;
  }

  // Employee login
  static Future<Map<String, dynamic>?> loginEmployee(
    String identifier,
    String password,
  ) async {
    try {
      if (kDebugMode) {
        print('🔐 Employee login attempt: $identifier');
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/auth/employee/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'identifier': identifier, 'password': password}),
      );

      if (kDebugMode) {
        print(
          '📥 Employee login response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data;
      } else {
        throw Exception(
          'Failed to login: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Employee login error: $e');
      }
      rethrow;
    }
  }
}
