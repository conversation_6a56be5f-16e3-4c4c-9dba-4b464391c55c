# 📋 ملخص شامل لـ API - الموظفين والإشعارات

## 👥 **API الموظفين (Employees)**

### 🔐 **تسجيل دخول الموظف**
```javascript
// POST /api/auth/employee/login
{
  "identifier": "emp12345",  // username أو phone
  "password": "emp123"
}
```

### ➕ **إنشاء موظف جديد**
```javascript
// POST /api/employees
{
  "full_name": "محمد أحمد الخطيب",           // مطلوب
  "phone": "********",                      // اختياري
  "contract_type": "دوام كامل",             // مطلوب
  "employee_type": "مدرس",                 // مطلوب
  "job_status": "نشط",                     // مطلوب
  "automatic_number": "AUTO001",            // اختياري
  "financial_number": "FIN001",             // اختياري
  "state_cooperative_number": "COOP001",    // اختياري
  "bank_account_number": "BANK001",         // اختياري
  "username": "emp001",                     // اختياري - فريد
  "password": "emp123"                      // اختياري
}
```

### ✏️ **تحديث موظف**
```javascript
// PUT /api/employees/{id}
{
  "full_name": "محمد أحمد الخطيب",           // مطلوب
  "phone": "********",                      // اختياري
  "contract_type": "دوام جزئي",             // مطلوب
  "employee_type": "إداري",                // مطلوب
  "job_status": "نشط",                     // مطلوب
  "automatic_number": "AUTO001",            // اختياري
  "financial_number": "FIN001",             // اختياري
  "state_cooperative_number": "COOP001",    // اختياري
  "bank_account_number": "BANK001",         // اختياري
  "username": "emp001",                     // اختياري - فريد
  "password": "new_password"                // اختياري
}
```

### 📊 **قراءة بيانات الموظفين**
```javascript
// GET /api/employees                    // جميع الموظفين
// GET /api/employees/{id}               // موظف محدد
```

### 🗑️ **حذف موظف**
```javascript
// DELETE /api/employees/{id}                    // حذف موظف واحد
// DELETE /api/employees/admin/delete-all        // حذف جميع الموظفين
```

### 📋 **الحصول على خيارات القوائم المنسدلة**
```javascript
// GET /api/employees/options/contract-types     // أنواع العقود
// GET /api/employees/options/employee-types     // أنواع الموظفين
// GET /api/employees/options/job-statuses       // حالات الوظيفة
```

---

## 🔔 **API الإشعارات (Notifications)**

### ➕ **إنشاء إشعار جديد**
```javascript
// POST /api/notifications
{
  "title": "إشعار مهم",                     // مطلوب
  "content": "محتوى الإشعار هنا",           // مطلوب
  "type": "info",                          // مطلوب: info, warning, success, error
  "priority": "high",                      // مطلوب: low, medium, high
  "target_audience": "students",           // مطلوب: all, students, employees, specific
  "recipient_ids": [1, 2, 3],             // مطلوب إذا كان target_audience = "specific"
  "scheduled_at": "2025-06-12 10:00:00",  // اختياري - للإشعارات المجدولة
  "expires_at": "2025-06-20 23:59:59",    // اختياري - تاريخ انتهاء الصلاحية
  "attachment_path": "/uploads/file.pdf",  // اختياري - مسار المرفق
  "attachment_name": "ملف مهم.pdf",        // اختياري - اسم المرفق
  "attachment_size": 1024000,             // اختياري - حجم المرفق بالبايت
  "is_active": true                       // اختياري - افتراضي true
}
```

### ✏️ **تحديث إشعار**
```javascript
// PUT /api/notifications/{id}
{
  "title": "إشعار محدث",
  "content": "محتوى محدث",
  "type": "warning",
  "priority": "medium",
  "target_audience": "employees",
  "is_active": false
}
```

### 📤 **إرسال إشعار**
```javascript
// POST /api/notifications/send
{
  "notification_id": 123  // ID الإشعار المراد إرساله
}
```

### 📖 **تمييز إشعار كمقروء**
```javascript
// POST /api/notifications/{id}/mark-read
```

### 📊 **قراءة الإشعارات**
```javascript
// GET /api/notifications                           // جميع الإشعارات
// GET /api/notifications/{id}                      // إشعار محدد
// GET /api/notifications/student/notifications     // إشعارات الطلاب
// GET /api/notifications/employee/notifications    // إشعارات الموظفين
```

### 🗑️ **حذف إشعارات**
```javascript
// DELETE /api/notifications/{id}                   // حذف إشعار واحد

// DELETE /api/notifications/bulk/delete            // حذف متعدد
{
  "notification_ids": [1, 2, 3, 4]
}
```

### 🔄 **تحديث حالة إشعارات متعددة**
```javascript
// PATCH /api/notifications/bulk/update-status
{
  "notification_ids": [1, 2, 3],
  "status": "read"  // أو "unread"
}
```

---

## 🎯 **معايير التصفية للفلتر**

### 👥 **تصفية الموظفين**
```javascript
// GET /api/employees?filter_params
{
  "contract_type": "دوام كامل",      // تصفية حسب نوع العقد
  "employee_type": "مدرس",          // تصفية حسب نوع الموظف
  "job_status": "نشط",             // تصفية حسب حالة الوظيفة
  "search": "محمد",                // البحث في الاسم
  "page": 1,                       // رقم الصفحة
  "per_page": 10                   // عدد العناصر في الصفحة
}
```

### 🔔 **تصفية الإشعارات**
```javascript
// GET /api/notifications?filter_params
{
  "type": "info",                  // نوع الإشعار
  "priority": "high",              // أولوية الإشعار
  "target_audience": "students",   // الجمهور المستهدف
  "is_active": true,               // حالة النشاط
  "status": "read",                // حالة القراءة (read/unread)
  "date_from": "2025-06-01",       // من تاريخ
  "date_to": "2025-06-30",         // إلى تاريخ
  "search": "مهم",                 // البحث في العنوان والمحتوى
  "page": 1,
  "per_page": 10
}
```

---

## 📋 **قوائم القيم المسموحة**

### 👥 **الموظفين**
```javascript
// أنواع العقود
contract_types: ["دوام كامل", "دوام جزئي", "مؤقت", "استشاري"]

// أنواع الموظفين  
employee_types: ["مدرس", "إداري", "فني", "عامل", "مشرف"]

// حالات الوظيفة
job_statuses: ["نشط", "غير نشط", "إجازة", "مفصول"]
```

### 🔔 **الإشعارات**
```javascript
// أنواع الإشعارات
types: ["info", "warning", "success", "error"]

// مستويات الأولوية
priorities: ["low", "medium", "high"]

// الجمهور المستهدف
target_audiences: ["all", "students", "employees", "specific"]

// حالات القراءة
statuses: ["read", "unread"]
```

---

## 🧪 **أمثلة PowerShell للاختبار**

### 👥 **الموظفين**
```powershell
# إنشاء موظف
$employeeData = @{
    full_name = "سارة علي حسن"
    phone = "76543210"
    contract_type = "دوام كامل"
    employee_type = "مدرس"
    job_status = "نشط"
    username = "sara_ali"
    password = "emp123"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer YOUR_TOKEN"
    "Content-Type" = "application/json"
}

Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/employees" -Method POST -Headers $headers -Body $employeeData

# تسجيل دخول موظف
Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/auth/employee/login" -Method POST -ContentType "application/json" -Body '{"identifier":"sara_ali","password":"emp123"}'

# الحصول على جميع الموظفين
Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/employees" -Method GET -Headers $headers

# تصفية الموظفين
Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/employees?contract_type=دوام كامل&job_status=نشط" -Method GET -Headers $headers
```

### 🔔 **الإشعارات**
```powershell
# إنشاء إشعار
$notificationData = @{
    title = "إشعار للطلاب"
    content = "يرجى مراجعة الجدول الدراسي الجديد"
    type = "info"
    priority = "high"
    target_audience = "students"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications" -Method POST -Headers $headers -Body $notificationData

# إرسال إشعار
Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications/send" -Method POST -Headers $headers -Body '{"notification_id":1}'

# الحصول على جميع الإشعارات
Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications" -Method GET -Headers $headers

# تصفية الإشعارات
Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications?type=info&priority=high&target_audience=students" -Method GET -Headers $headers

# تمييز إشعار كمقروء
Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications/1/mark-read" -Method POST -Headers $headers

# حذف متعدد للإشعارات
$bulkDeleteData = @{
    notification_ids = @(1, 2, 3)
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications/bulk/delete" -Method DELETE -Headers $headers -Body $bulkDeleteData
```

---

## 📊 **جداول التحقق من صحة البيانات**

### 👥 **الموظفين - قواعد التحقق**

| Field | Type | Required | Max Length | Unique | Notes |
|-------|------|----------|------------|--------|-------|
| `full_name` | string | ✅ Yes | 255 | ❌ No | - |
| `phone` | string | ❌ No | 50 | ❌ No | - |
| `contract_type` | string | ✅ Yes | 255 | ❌ No | من القائمة المحددة |
| `employee_type` | string | ✅ Yes | 255 | ❌ No | من القائمة المحددة |
| `job_status` | string | ✅ Yes | 255 | ❌ No | من القائمة المحددة |
| `automatic_number` | string | ❌ No | 100 | ❌ No | - |
| `financial_number` | string | ❌ No | 100 | ❌ No | - |
| `state_cooperative_number` | string | ❌ No | 100 | ❌ No | - |
| `bank_account_number` | string | ❌ No | 100 | ❌ No | - |
| `username` | string | ❌ No | 50 | ✅ Yes | يتم إنشاؤه تلقائياً إذا لم يُحدد |
| `password` | string | ❌ No | - | ❌ No | Min 3 chars, افتراضي: emp123 |

### 🔔 **الإشعارات - قواعد التحقق**

| Field | Type | Required | Max Length | Notes |
|-------|------|----------|------------|-------|
| `title` | string | ✅ Yes | 255 | - |
| `content` | text | ✅ Yes | - | - |
| `type` | string | ✅ Yes | - | info, warning, success, error |
| `priority` | string | ✅ Yes | - | low, medium, high |
| `target_audience` | string | ✅ Yes | - | all, students, employees, specific |
| `recipient_ids` | array | ❌ Conditional | - | مطلوب إذا target_audience = specific |
| `scheduled_at` | datetime | ❌ No | - | Format: Y-m-d H:i:s |
| `expires_at` | datetime | ❌ No | - | Format: Y-m-d H:i:s |
| `attachment_path` | string | ❌ No | 500 | - |
| `attachment_name` | string | ❌ No | 255 | - |
| `attachment_size` | integer | ❌ No | - | بالبايت |
| `is_active` | boolean | ❌ No | - | افتراضي: true |

---

## 🔐 **متطلبات المصادقة**

### 🔑 **Admin Token Required**
- ✅ إنشاء/تحديث/حذف الموظفين
- ✅ إنشاء/تحديث/حذف الإشعارات
- ✅ إرسال الإشعارات
- ✅ العمليات المجمعة للإشعارات

### 🔑 **User Token Required**
- ✅ قراءة الإشعارات الشخصية
- ✅ تمييز الإشعارات كمقروءة

### 🔓 **No Authentication Required**
- ✅ تسجيل دخول الموظفين
- ✅ تسجيل دخول المديرين

---

## 🎯 **نصائح للتطبيق**

### 📱 **للفلتر في التطبيق**
1. **استخدم القوائم المنسدلة** للحقول المحددة مسبقاً
2. **طبق التصفية التدريجية** (Progressive Filtering)
3. **احفظ معايير التصفية** في التفضيلات المحلية
4. **استخدم البحث النصي** للحقول النصية
5. **طبق التصفية حسب التاريخ** للإشعارات

### 🔄 **للتحديث في الوقت الفعلي**
1. **استخدم WebSockets** للإشعارات الجديدة
2. **طبق Polling** للتحديثات الدورية
3. **استخدم Push Notifications** للإشعارات المهمة

### 💾 **لإدارة البيانات**
1. **طبق Pagination** للقوائم الطويلة
2. **استخدم Caching** للبيانات المتكررة
3. **طبق Lazy Loading** للصور والمرفقات

---

## 🚀 **الخلاصة**

هذا الدليل يوفر جميع المعلومات اللازمة لتطبيق نظام إدارة الموظفين والإشعارات مع إمكانيات التصفية المتقدمة. جميع الـ APIs محدثة وتتوافق مع قاعدة البيانات الحالية.

**تم اختبار جميع الـ APIs وهي تعمل بشكل صحيح!** ✅
