import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/notification.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

class NotificationService {
  static const String baseUrl = '${ApiConstants.baseUrl}/notifications';
  static List<AppNotification>? _cachedNotifications;
  static DateTime? _lastCacheTime;
  static const Duration cacheTimeout = Duration(minutes: 2);

  // Clear cache
  static void clearCache() {
    _cachedNotifications = null;
    _lastCacheTime = null;
  }

  // Check if cache is valid
  static bool get _isCacheValid {
    if (_cachedNotifications == null || _lastCacheTime == null) return false;
    return DateTime.now().difference(_lastCacheTime!) < cacheTimeout;
  }

  // Get all notifications
  static Future<List<AppNotification>> getNotifications({
    String? type,
    String? priority,
    String? targetAudience,
    bool? isActive,
    String? status,
    String? dateFrom,
    String? dateTo,
    String? search,
    int? page,
    int? perPage,
  }) async {
    try {
      // Use cache if valid and no filters
      if (_isCacheValid &&
          type == null &&
          priority == null &&
          targetAudience == null &&
          isActive == null &&
          status == null &&
          search == null) {
        if (kDebugMode) {
          print('📋 Using cached notifications data');
        }
        return _cachedNotifications!;
      }

      final token = await AuthService.getStoredToken();
      if (token == null) {
        if (kDebugMode) {
          print('❌ No authentication token found');
          print('🔧 Please login first to get notifications');
        }
        throw Exception('No authentication token found. Please login first.');
      }

      if (kDebugMode) {
        print('✅ Found token: ${token.substring(0, 20)}...');
      }

      // Build query parameters
      final queryParams = <String, String>{};
      if (type != null) queryParams['type'] = type;
      if (priority != null) queryParams['priority'] = priority;
      if (targetAudience != null) {
        queryParams['target_audience'] = targetAudience;
      }
      if (isActive != null) queryParams['is_active'] = isActive.toString();
      if (status != null) queryParams['status'] = status;
      if (dateFrom != null) queryParams['date_from'] = dateFrom;
      if (dateTo != null) queryParams['date_to'] = dateTo;
      if (search != null) queryParams['search'] = search;
      if (page != null) queryParams['page'] = page.toString();
      if (perPage != null) queryParams['per_page'] = perPage.toString();

      final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);

      if (kDebugMode) {
        print(
          '🔑 Using token for notifications API: ${token.substring(0, 20)}...',
        );
        print('📤 GET $uri');
        print('🌐 Full API URL: $baseUrl');
        print('📋 Query params: $queryParams');
      }

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print('📥 Get notifications response: ${response.statusCode}');
        if (response.body.length > 200) {
          print(
            '📄 Response body (first 200 chars): ${response.body.substring(0, 200)}...',
          );
        } else {
          print('📄 Response body: ${response.body}');
        }
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (kDebugMode) {
          print('📊 Parsed response data keys: ${data.keys.toList()}');
        }

        final dynamic notificationsData = data['data'] ?? data;

        if (notificationsData is! List) {
          if (kDebugMode) {
            print('⚠️ Expected list but got: ${notificationsData.runtimeType}');
            print('📄 Data content: $notificationsData');
          }
          return [];
        }

        final List<dynamic> notificationsJson = notificationsData;

        final notifications = notificationsJson.map((json) {
          if (kDebugMode) {
            // Debug log for attachments
            if (json['attachments'] != null ||
                json['attachment_names'] != null) {
              print('🔍 Notification with attachments found:');
              print('   Title: ${json['title']}');
              print('   Attachments field: ${json['attachments']}');
              print('   Attachment names: ${json['attachment_names']}');
              print('   Attachment count: ${json['attachment_count']}');
              print('   Has attachments: ${json['has_attachments']}');
            }
          }
          return AppNotification.fromJson(json);
        }).toList();

        if (kDebugMode) {
          print('✅ Successfully parsed ${notifications.length} notifications');

          // Check for notifications with attachments
          final notificationsWithAttachments = notifications
              .where((n) => n.hasAttachment)
              .toList();
          if (notificationsWithAttachments.isNotEmpty) {
            print(
              '📎 Found ${notificationsWithAttachments.length} notifications with attachments:',
            );
            for (var notification in notificationsWithAttachments) {
              print(
                '   - ${notification.title}: ${notification.attachmentCount} attachments',
              );
              print('     All attachments: ${notification.allAttachments}');
            }
          }
        }

        // Cache only if no filters applied
        if (type == null &&
            priority == null &&
            targetAudience == null &&
            isActive == null &&
            status == null &&
            search == null) {
          _cachedNotifications = notifications;
          _lastCacheTime = DateTime.now();
          if (kDebugMode) {
            print('💾 Cached ${notifications.length} notifications');
          }
        }

        return notifications;
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized - Please login again');
      } else {
        throw Exception(
          'Failed to get notifications: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting notifications: $e');
        print('🔧 Please ensure Laravel API is running and admin is logged in');
        print('🌐 API URL: $baseUrl');
      }

      // Return cached data if available and error is network related
      if (_cachedNotifications != null &&
          (e.toString().contains('Failed host lookup') ||
              e.toString().contains('Connection refused') ||
              e.toString().contains('SocketException'))) {
        if (kDebugMode) {
          print('📱 Using cached data due to network error');
        }
        return _cachedNotifications!;
      }

      // Provide more specific error messages
      String errorMessage = 'Failed to connect to Laravel API.';
      if (e.toString().contains('401') ||
          e.toString().contains('Unauthorized')) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (e.toString().contains('Failed host lookup') ||
          e.toString().contains('Connection refused')) {
        errorMessage =
            'Cannot connect to server. Please check if Laravel API is running.';
      } else if (e.toString().contains('No authentication token')) {
        errorMessage = 'No authentication token found. Please login first.';
      }

      throw Exception(errorMessage);
    }
  }

  // Get single notification
  static Future<AppNotification?> getNotification(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AppNotification.fromJson(data['data'] ?? data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to get notification: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting notification: $e');
      }
      rethrow;
    }
  }

  // Create notification
  static Future<AppNotification?> createNotification(
    Map<String, dynamic> notificationData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('🔍 STEP 5: Inside NotificationService.createNotification');
        print('📤 Creating notification: $notificationData');
        print('🔍 Data types check:');
        notificationData.forEach((key, value) {
          print('   $key: ${value.runtimeType} = $value');
        });
        print('🔍 STEP 6: About to call json.encode...');
      }

      String jsonBody;
      try {
        jsonBody = json.encode(notificationData);
        if (kDebugMode) {
          print('✅ STEP 7: json.encode successful - NO ERROR IN JSON ENCODING');
          print('📤 JSON body: $jsonBody');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ STEP 7: ERROR IN JSON ENCODING: $e');
          print('🚨 CONFIRMED: _Namespace error is in JSON ENCODING (FLUTTER)');
        }
        rethrow;
      }

      if (kDebugMode) {
        print('🔍 STEP 8: About to make HTTP POST request...');
      }

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonBody,
      );

      if (kDebugMode) {
        print('🔍 STEP 9: Received HTTP response');
        print('📥 Response status: ${response.statusCode}');
        print('📥 Response headers: ${response.headers}');
        print(
          '📥 Response body (first 500 chars): ${response.body.length > 500 ? response.body.substring(0, 500) + "..." : response.body}',
        );
        print('📥 Full response body: ${response.body}');

        if (response.statusCode != 200 && response.statusCode != 201) {
          print('🚨 CONFIRMED: ERROR IS IN LARAVEL API');
          print('🚨 Laravel returned error status: ${response.statusCode}');
        } else {
          print('✅ Laravel returned success status: ${response.statusCode}');
        }
      }

      if (response.statusCode == 201 || response.statusCode == 200) {
        try {
          final data = json.decode(response.body);
          if (kDebugMode) {
            print('✅ STEP 10: Successfully parsed Laravel response');
            print('📋 Response data structure: $data');
            print('📋 Data type: ${data.runtimeType}');
            if (data is Map) {
              print('📋 Available keys: ${data.keys.toList()}');
            }
          }

          clearCache(); // Clear cache after creating

          // Enhanced response handling for different Laravel response formats
          Map<String, dynamic> notificationData;

          if (data is Map) {
            // Try different common Laravel response structures
            if (data.containsKey('data') && data['data'] is Map) {
              // Standard Laravel API Resource format: {"data": {...}}
              notificationData = Map<String, dynamic>.from(data['data']);
            } else if (data.containsKey('notification') &&
                data['notification'] is Map) {
              // Custom notification format: {"notification": {...}}
              notificationData = Map<String, dynamic>.from(
                data['notification'],
              );
            } else if (data.containsKey('success') &&
                data.containsKey('message')) {
              // Laravel success response format: {"success": true, "message": "...", "data": {...}}
              if (data.containsKey('data') && data['data'] is Map) {
                notificationData = Map<String, dynamic>.from(data['data']);
              } else {
                // Create a minimal notification object from available data
                notificationData = {
                  'id': data['id'] ?? 0,
                  'title': data['title'] ?? 'إشعار جديد',
                  'content': data['message'] ?? data['content'] ?? '',
                  'type': data['type'] ?? 'info',
                  'priority': data['priority'] ?? 'medium',
                  'target_audience': data['target_audience'] ?? 'all',
                  'is_active': data['is_active'] ?? true,
                  'created_at': DateTime.now().toIso8601String(),
                  'updated_at': DateTime.now().toIso8601String(),
                };
              }
            } else if (data.containsKey('id') || data.containsKey('title')) {
              // Direct notification object
              notificationData = Map<String, dynamic>.from(data);
            } else {
              // Unknown format - create a basic notification
              if (kDebugMode) {
                print(
                  '⚠️ Unknown response format, creating basic notification',
                );
              }
              notificationData = {
                'id': DateTime.now().millisecondsSinceEpoch,
                'title': 'إشعار جديد',
                'content': 'تم إنشاء الإشعار بنجاح',
                'type': 'info',
                'priority': 'medium',
                'target_audience': 'all',
                'is_active': true,
                'created_at': DateTime.now().toIso8601String(),
                'updated_at': DateTime.now().toIso8601String(),
              };
            }
          } else {
            throw Exception('Response is not a Map: ${data.runtimeType}');
          }

          if (kDebugMode) {
            print('📋 Final notification data to parse: $notificationData');
          }

          return AppNotification.fromJson(notificationData);
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error parsing Laravel response: $e');
            print('📋 Raw response body: ${response.body}');
          }

          // If parsing fails but we got a success status, create a basic notification
          if (kDebugMode) {
            print('🔧 Creating fallback notification due to parsing error');
            print(
              '🔧 This means Laravel created the notification but sent unexpected response format',
            );
          }

          // Try to extract basic info from response even if parsing failed
          String title = 'إشعار جديد';
          String content = 'تم إنشاء الإشعار بنجاح';

          try {
            // Try to extract title/message from raw response
            if (response.body.contains('"title"')) {
              final titleMatch = RegExp(
                r'"title"\s*:\s*"([^"]*)"',
              ).firstMatch(response.body);
              if (titleMatch != null) {
                title = titleMatch.group(1) ?? title;
              }
            }
            if (response.body.contains('"message"') ||
                response.body.contains('"content"')) {
              final messageMatch = RegExp(
                r'"(?:message|content)"\s*:\s*"([^"]*)"',
              ).firstMatch(response.body);
              if (messageMatch != null) {
                content = messageMatch.group(1) ?? content;
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('🔧 Could not extract title/content from response: $e');
            }
          }

          return AppNotification(
            id: DateTime.now().millisecondsSinceEpoch,
            title: title,
            content: content,
            type: 'info',
            priority: 'medium',
            targetAudience: 'all',
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
        }
      } else {
        if (kDebugMode) {
          print('🚨 Laravel API Error Details:');
          print('   Status Code: ${response.statusCode}');
          print('   Response Body: ${response.body}');

          try {
            final errorData = json.decode(response.body);
            print('   Parsed Error: $errorData');
            if (errorData['message'] != null) {
              print('   Error Message: ${errorData['message']}');
            }
            if (errorData['errors'] != null) {
              print('   Validation Errors: ${errorData['errors']}');
            }
          } catch (e) {
            print('   Could not parse error response as JSON');
          }
        }
        // Check for specific Laravel errors
        String errorMessage = 'Laravel API Error (${response.statusCode})';

        if (response.statusCode == 422) {
          errorMessage = 'خطأ في البيانات المرسلة (Validation Error)';
        } else if (response.statusCode == 401) {
          errorMessage = 'خطأ في المصادقة (Authentication Error)';
        } else if (response.statusCode == 403) {
          errorMessage = 'ليس لديك صلاحية (Authorization Error)';
        } else if (response.statusCode == 404) {
          errorMessage = 'الرابط غير موجود (Endpoint Not Found)';
        } else if (response.statusCode == 500) {
          errorMessage = 'خطأ في الخادم (Server Error)';
        }

        throw Exception('$errorMessage: ${response.body}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
      rethrow;
    }
  }

  // Update notification
  static Future<AppNotification?> updateNotification(
    int id,
    Map<String, dynamic> notificationData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Updating notification $id: $notificationData');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(notificationData),
      );

      if (kDebugMode) {
        print(
          '📥 Update notification response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        clearCache(); // Clear cache after updating
        return AppNotification.fromJson(data['data'] ?? data);
      } else {
        throw Exception(
          'Failed to update notification: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating notification: $e');
      }
      rethrow;
    }
  }

  // Delete notification
  static Future<bool> deleteNotification(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.delete(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        clearCache(); // Clear cache after deleting
        return true;
      } else {
        throw Exception(
          'Failed to delete notification: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting notification: $e');
      }
      rethrow;
    }
  }

  // Send notification
  static Future<bool> sendNotification(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Sending notification: $notificationId');
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/notifications/send'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'notification_id': notificationId}),
      );

      if (kDebugMode) {
        print(
          '📥 Send notification response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(
          'Failed to send notification: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending notification: $e');
      }
      rethrow;
    }
  }

  // Mark notification as read
  static Future<bool> markAsRead(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/$notificationId/mark-read'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        clearCache(); // Clear cache after marking as read
        return true;
      } else {
        throw Exception(
          'Failed to mark notification as read: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
      rethrow;
    }
  }
}
