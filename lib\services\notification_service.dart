import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/notification.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

class NotificationService {
  static const String baseUrl = '${ApiConstants.baseUrl}/notifications';
  static List<AppNotification>? _cachedNotifications;
  static DateTime? _lastCacheTime;
  static const Duration cacheTimeout = Duration(minutes: 2);

  // Clear cache
  static void clearCache() {
    _cachedNotifications = null;
    _lastCacheTime = null;
  }

  // Check if cache is valid
  static bool get _isCacheValid {
    if (_cachedNotifications == null || _lastCacheTime == null) return false;
    return DateTime.now().difference(_lastCacheTime!) < cacheTimeout;
  }

  // Get all notifications
  static Future<List<AppNotification>> getNotifications({
    String? type,
    String? priority,
    String? targetAudience,
    bool? isActive,
    String? status,
    String? dateFrom,
    String? dateTo,
    String? search,
    int? page,
    int? perPage,
  }) async {
    try {
      // Use cache if valid and no filters
      if (_isCacheValid &&
          type == null &&
          priority == null &&
          targetAudience == null &&
          isActive == null &&
          status == null &&
          search == null) {
        if (kDebugMode) {
          print('📋 Using cached notifications data');
        }
        return _cachedNotifications!;
      }

      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Build query parameters
      final queryParams = <String, String>{};
      if (type != null) queryParams['type'] = type;
      if (priority != null) queryParams['priority'] = priority;
      if (targetAudience != null) {
        queryParams['target_audience'] = targetAudience;
      }
      if (isActive != null) queryParams['is_active'] = isActive.toString();
      if (status != null) queryParams['status'] = status;
      if (dateFrom != null) queryParams['date_from'] = dateFrom;
      if (dateTo != null) queryParams['date_to'] = dateTo;
      if (search != null) queryParams['search'] = search;
      if (page != null) queryParams['page'] = page.toString();
      if (perPage != null) queryParams['per_page'] = perPage.toString();

      final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);

      if (kDebugMode) {
        print(
          '🔑 Using token for notifications API: ${token.substring(0, 20)}...',
        );
        print('📤 GET $uri');
      }

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print(
          '📥 Get notifications response: ${response.statusCode} - ${response.body.substring(0, 200)}...',
        );
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> notificationsJson = data['data'] ?? data;
        final notifications = notificationsJson
            .map((json) => AppNotification.fromJson(json))
            .toList();

        // Cache only if no filters applied
        if (type == null &&
            priority == null &&
            targetAudience == null &&
            isActive == null &&
            status == null &&
            search == null) {
          _cachedNotifications = notifications;
          _lastCacheTime = DateTime.now();
          if (kDebugMode) {
            print('💾 Cached ${notifications.length} notifications');
          }
        }

        return notifications;
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized - Please login again');
      } else {
        throw Exception(
          'Failed to get notifications: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting notifications: $e');
        print('🔧 Please ensure Laravel API is running and admin is logged in');
      }

      // Return cached data if available and error is network related
      if (_cachedNotifications != null &&
          e.toString().contains('Failed host lookup')) {
        if (kDebugMode) {
          print('📱 Using cached data due to network error');
        }
        return _cachedNotifications!;
      }

      // Return demo data temporarily if API is not available
      if (kDebugMode) {
        print('⚠️ API not available, returning demo data for development');
      }
      return _generateDemoNotifications();
    }
  }

  // Get single notification
  static Future<AppNotification?> getNotification(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AppNotification.fromJson(data['data'] ?? data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to get notification: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting notification: $e');
      }
      rethrow;
    }
  }

  // Create notification
  static Future<AppNotification?> createNotification(
    Map<String, dynamic> notificationData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Creating notification: $notificationData');
      }

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(notificationData),
      );

      if (kDebugMode) {
        print(
          '📥 Create notification response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = json.decode(response.body);
        clearCache(); // Clear cache after creating
        return AppNotification.fromJson(data['data'] ?? data);
      } else {
        throw Exception(
          'Failed to create notification: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
      rethrow;
    }
  }

  // Update notification
  static Future<AppNotification?> updateNotification(
    int id,
    Map<String, dynamic> notificationData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Updating notification $id: $notificationData');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(notificationData),
      );

      if (kDebugMode) {
        print(
          '📥 Update notification response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        clearCache(); // Clear cache after updating
        return AppNotification.fromJson(data['data'] ?? data);
      } else {
        throw Exception(
          'Failed to update notification: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating notification: $e');
      }
      rethrow;
    }
  }

  // Delete notification
  static Future<bool> deleteNotification(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.delete(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        clearCache(); // Clear cache after deleting
        return true;
      } else {
        throw Exception(
          'Failed to delete notification: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting notification: $e');
      }
      rethrow;
    }
  }

  // Send notification
  static Future<bool> sendNotification(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Sending notification: $notificationId');
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/notifications/send'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'notification_id': notificationId}),
      );

      if (kDebugMode) {
        print(
          '📥 Send notification response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(
          'Failed to send notification: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending notification: $e');
      }
      rethrow;
    }
  }

  // Mark notification as read
  static Future<bool> markAsRead(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/$notificationId/mark-read'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        clearCache(); // Clear cache after marking as read
        return true;
      } else {
        throw Exception(
          'Failed to mark notification as read: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
      rethrow;
    }
  }

  // Generate demo notifications for development
  static List<AppNotification> _generateDemoNotifications() {
    final demoNotifications = <AppNotification>[];
    final types = ['info', 'warning', 'success', 'error'];
    final priorities = ['low', 'medium', 'high'];
    final audiences = ['all', 'students', 'employees', 'specific'];
    final titles = [
      'إشعار مهم للطلاب',
      'تحديث في النظام الأكاديمي',
      'إجازة رسمية - عطلة نهاية الأسبوع',
      'اجتماع طارئ لجميع الموظفين',
      'تحديث الجدول الدراسي للفصل الجديد',
      'صيانة النظام - توقف مؤقت',
      'إعلان نتائج الامتحانات النهائية',
      'ورشة عمل تدريبية جديدة',
      'تغيير في مواعيد المحاضرات',
      'إشعار خاص للموظفين الإداريين',
      'تحديث سياسة الحضور والغياب',
      'إعلان عن منح دراسية جديدة',
    ];

    final contents = [
      'يرجى من جميع الطلاب مراجعة الإعلانات الجديدة على لوحة الإعلانات.',
      'سيتم تحديث النظام الأكاديمي يوم الجمعة من الساعة 2 إلى 4 مساءً.',
      'إجازة رسمية يومي الخميس والجمعة القادمين بمناسبة العطلة الوطنية.',
      'اجتماع طارئ لجميع الموظفين غداً الساعة 10 صباحاً في قاعة الاجتماعات.',
      'تم تحديث الجدول الدراسي للفصل الجديد. يرجى مراجعة المواعيد الجديدة.',
      'صيانة دورية للنظام ستؤدي إلى توقف مؤقت للخدمات من 12 إلى 2 ظهراً.',
      'تم الإعلان عن نتائج الامتحانات النهائية. يمكن الاطلاع عليها من خلال النظام.',
      'ورشة عمل تدريبية حول استخدام التقنيات الحديثة في التعليم.',
      'تغيير في مواعيد بعض المحاضرات بسبب ظروف طارئة.',
      'إشعار خاص للموظفين الإداريين حول تحديث الإجراءات الإدارية.',
      'تحديث سياسة الحضور والغياب للطلاب والموظفين.',
      'إعلان عن منح دراسية جديدة للطلاب المتفوقين.',
    ];

    for (int i = 0; i < titles.length; i++) {
      demoNotifications.add(
        AppNotification(
          id: i + 1,
          title: titles[i],
          content: contents[i],
          type: types[i % types.length],
          priority: priorities[i % priorities.length],
          targetAudience: audiences[i % audiences.length],
          recipientIds: audiences[i % audiences.length] == 'specific'
              ? [1, 2, 3]
              : null,
          scheduledAt: i % 5 == 0
              ? DateTime.now().add(Duration(hours: i + 1))
              : null,
          expiresAt: i % 3 == 0 ? DateTime.now().add(Duration(days: 7)) : null,
          attachmentPath: i % 4 == 0 ? '/uploads/document_${i + 1}.pdf' : null,
          attachmentName: i % 4 == 0 ? 'مستند_${i + 1}.pdf' : null,
          attachmentSize: i % 4 == 0
              ? (1024 * (i + 1) * 100) // Random size
              : null,
          isActive: i % 5 != 0, // Make some inactive
          status: i % 3 == 0 ? 'read' : 'unread',
          createdAt: DateTime.now().subtract(Duration(hours: i * 2)),
          updatedAt: DateTime.now().subtract(Duration(hours: i)),
        ),
      );
    }

    if (kDebugMode) {
      print('📊 Generated ${demoNotifications.length} demo notifications');
    }

    return demoNotifications;
  }
}
