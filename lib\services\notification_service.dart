import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/notification.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

class NotificationService {
  static const String baseUrl = '${ApiConstants.baseUrl}/notifications';
  static List<AppNotification>? _cachedNotifications;
  static DateTime? _lastCacheTime;
  static const Duration cacheTimeout = Duration(minutes: 2);

  // Clear cache
  static void clearCache() {
    _cachedNotifications = null;
    _lastCacheTime = null;
  }

  // Check if cache is valid
  static bool get _isCacheValid {
    if (_cachedNotifications == null || _lastCacheTime == null) return false;
    return DateTime.now().difference(_lastCacheTime!) < cacheTimeout;
  }

  // Get all notifications
  static Future<List<AppNotification>> getNotifications({
    String? type,
    String? priority,
    String? targetAudience,
    bool? isActive,
    String? status,
    String? dateFrom,
    String? dateTo,
    String? search,
    int? page,
    int? perPage,
  }) async {
    try {
      // Use cache if valid and no filters
      if (_isCacheValid &&
          type == null &&
          priority == null &&
          targetAudience == null &&
          isActive == null &&
          status == null &&
          search == null) {
        if (kDebugMode) {
          print('📋 Using cached notifications data');
        }
        return _cachedNotifications!;
      }

      final token = await AuthService.getStoredToken();
      if (token == null) {
        if (kDebugMode) {
          print('❌ No authentication token found');
          print('🔧 Please login first to get notifications');
        }
        throw Exception('No authentication token found. Please login first.');
      }

      if (kDebugMode) {
        print('✅ Found token: ${token.substring(0, 20)}...');
      }

      // Build query parameters
      final queryParams = <String, String>{};
      if (type != null) queryParams['type'] = type;
      if (priority != null) queryParams['priority'] = priority;
      if (targetAudience != null) {
        queryParams['target_audience'] = targetAudience;
      }
      if (isActive != null) queryParams['is_active'] = isActive.toString();
      if (status != null) queryParams['status'] = status;
      if (dateFrom != null) queryParams['date_from'] = dateFrom;
      if (dateTo != null) queryParams['date_to'] = dateTo;
      if (search != null) queryParams['search'] = search;
      if (page != null) queryParams['page'] = page.toString();
      if (perPage != null) queryParams['per_page'] = perPage.toString();

      final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);

      if (kDebugMode) {
        print(
          '🔑 Using token for notifications API: ${token.substring(0, 20)}...',
        );
        print('📤 GET $uri');
        print('🌐 Full API URL: $baseUrl');
        print('📋 Query params: $queryParams');
      }

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print('📥 Get notifications response: ${response.statusCode}');
        if (response.body.length > 200) {
          print(
            '📄 Response body (first 200 chars): ${response.body.substring(0, 200)}...',
          );
        } else {
          print('📄 Response body: ${response.body}');
        }
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (kDebugMode) {
          print('📊 Parsed response data keys: ${data.keys.toList()}');
        }

        final dynamic notificationsData = data['data'] ?? data;

        if (notificationsData is! List) {
          if (kDebugMode) {
            print('⚠️ Expected list but got: ${notificationsData.runtimeType}');
            print('📄 Data content: $notificationsData');
          }
          return [];
        }

        final List<dynamic> notificationsJson = notificationsData;

        final notifications = notificationsJson
            .map((json) => AppNotification.fromJson(json))
            .toList();

        if (kDebugMode) {
          print('✅ Successfully parsed ${notifications.length} notifications');
        }

        // Cache only if no filters applied
        if (type == null &&
            priority == null &&
            targetAudience == null &&
            isActive == null &&
            status == null &&
            search == null) {
          _cachedNotifications = notifications;
          _lastCacheTime = DateTime.now();
          if (kDebugMode) {
            print('💾 Cached ${notifications.length} notifications');
          }
        }

        return notifications;
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized - Please login again');
      } else {
        throw Exception(
          'Failed to get notifications: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting notifications: $e');
        print('🔧 Please ensure Laravel API is running and admin is logged in');
        print('🌐 API URL: $baseUrl');
      }

      // Return cached data if available and error is network related
      if (_cachedNotifications != null &&
          (e.toString().contains('Failed host lookup') ||
              e.toString().contains('Connection refused') ||
              e.toString().contains('SocketException'))) {
        if (kDebugMode) {
          print('📱 Using cached data due to network error');
        }
        return _cachedNotifications!;
      }

      // Provide more specific error messages
      String errorMessage = 'Failed to connect to Laravel API.';
      if (e.toString().contains('401') ||
          e.toString().contains('Unauthorized')) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (e.toString().contains('Failed host lookup') ||
          e.toString().contains('Connection refused')) {
        errorMessage =
            'Cannot connect to server. Please check if Laravel API is running.';
      } else if (e.toString().contains('No authentication token')) {
        errorMessage = 'No authentication token found. Please login first.';
      }

      throw Exception(errorMessage);
    }
  }

  // Get single notification
  static Future<AppNotification?> getNotification(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AppNotification.fromJson(data['data'] ?? data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to get notification: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting notification: $e');
      }
      rethrow;
    }
  }

  // Create notification
  static Future<AppNotification?> createNotification(
    Map<String, dynamic> notificationData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('🔍 STEP 5: Inside NotificationService.createNotification');
        print('📤 Creating notification: $notificationData');
        print('🔍 Data types check:');
        notificationData.forEach((key, value) {
          print('   $key: ${value.runtimeType} = $value');
        });
        print('🔍 STEP 6: About to call json.encode...');
      }

      String jsonBody;
      try {
        jsonBody = json.encode(notificationData);
        if (kDebugMode) {
          print('✅ STEP 7: json.encode successful - NO ERROR IN JSON ENCODING');
          print('📤 JSON body: $jsonBody');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ STEP 7: ERROR IN JSON ENCODING: $e');
          print('🚨 CONFIRMED: _Namespace error is in JSON ENCODING (FLUTTER)');
        }
        rethrow;
      }

      if (kDebugMode) {
        print('🔍 STEP 8: About to make HTTP POST request...');
      }

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonBody,
      );

      if (kDebugMode) {
        print('🔍 STEP 9: Received HTTP response');
        print('📥 Response status: ${response.statusCode}');
        print('📥 Response headers: ${response.headers}');
        print('📥 Response body: ${response.body}');

        if (response.statusCode != 200 && response.statusCode != 201) {
          print('🚨 CONFIRMED: ERROR IS IN LARAVEL API');
          print('🚨 Laravel returned error status: ${response.statusCode}');
        }
      }

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = json.decode(response.body);
        clearCache(); // Clear cache after creating
        return AppNotification.fromJson(data['data'] ?? data);
      } else {
        if (kDebugMode) {
          print('🚨 Laravel API Error Details:');
          print('   Status Code: ${response.statusCode}');
          print('   Response Body: ${response.body}');

          try {
            final errorData = json.decode(response.body);
            print('   Parsed Error: $errorData');
            if (errorData['message'] != null) {
              print('   Error Message: ${errorData['message']}');
            }
            if (errorData['errors'] != null) {
              print('   Validation Errors: ${errorData['errors']}');
            }
          } catch (e) {
            print('   Could not parse error response as JSON');
          }
        }
        throw Exception(
          'Laravel API Error (${response.statusCode}): ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
      rethrow;
    }
  }

  // Update notification
  static Future<AppNotification?> updateNotification(
    int id,
    Map<String, dynamic> notificationData,
  ) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Updating notification $id: $notificationData');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(notificationData),
      );

      if (kDebugMode) {
        print(
          '📥 Update notification response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        clearCache(); // Clear cache after updating
        return AppNotification.fromJson(data['data'] ?? data);
      } else {
        throw Exception(
          'Failed to update notification: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating notification: $e');
      }
      rethrow;
    }
  }

  // Delete notification
  static Future<bool> deleteNotification(int id) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.delete(
        Uri.parse('$baseUrl/$id'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        clearCache(); // Clear cache after deleting
        return true;
      } else {
        throw Exception(
          'Failed to delete notification: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting notification: $e');
      }
      rethrow;
    }
  }

  // Send notification
  static Future<bool> sendNotification(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📤 Sending notification: $notificationId');
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/notifications/send'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'notification_id': notificationId}),
      );

      if (kDebugMode) {
        print(
          '📥 Send notification response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(
          'Failed to send notification: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending notification: $e');
      }
      rethrow;
    }
  }

  // Mark notification as read
  static Future<bool> markAsRead(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/$notificationId/mark-read'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        clearCache(); // Clear cache after marking as read
        return true;
      } else {
        throw Exception(
          'Failed to mark notification as read: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
      rethrow;
    }
  }
}
