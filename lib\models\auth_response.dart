class AuthResponse {
  final String token;
  final String userType;
  final Map<String, dynamic> user;
  final String? message;

  AuthResponse({
    required this.token,
    required this.userType,
    required this.user,
    this.message,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json, String userType) {
    // Handle Laravel API response structure
    Map<String, dynamic> userData = {};
    String token = '';

    // Extract token
    if (json['data'] != null && json['data']['token'] != null) {
      token = json['data']['token'];
    } else {
      token = json['token'] ?? json['access_token'] ?? '';
    }

    // Extract user data
    if (json['data'] != null && json['data']['user'] != null) {
      userData = json['data']['user'];
    } else if (json['user'] != null) {
      userData = json['user'];
    } else if (json['data'] != null) {
      userData = json['data'];
    }

    return AuthResponse(
      token: token,
      userType: userType,
      user: userData,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'user_type': userType,
      'user': user,
      'message': message,
    };
  }

  @override
  String toString() {
    return 'AuthResponse(token: ${token.substring(0, 20)}..., userType: $userType, message: $message)';
  }
}
