-- فحص قاعدة البيانات - Database Check

-- ===================================
-- 1. فحص الجداول الموجودة
-- Check existing tables
-- ===================================

SHOW TABLES;

-- ===================================
-- 2. فحص بنية جدول users
-- Check users table structure
-- ===================================

DESCRIBE users;

-- ===================================
-- 3. فحص المستخدمين الموجودين
-- Check existing users
-- ===================================

SELECT id, username, name, email, created_at FROM users;

-- ===================================
-- 4. البحث عن الأدمن
-- Search for admin
-- ===================================

-- البحث بـ username
SELECT * FROM users WHERE username = 'admin';

-- البحث بـ name
SELECT * FROM users WHERE name = 'admin';

-- البحث بـ email
SELECT * FROM users WHERE email = '<EMAIL>';

-- ===================================
-- 5. فحص جدول students
-- Check students table
-- ===================================

SELECT COUNT(*) as total_students FROM students;

-- عرض أول 5 طلاب
SELECT id, username, full_name, class, specialization FROM students LIMIT 5;

-- ===================================
-- 6. إنشاء أدمن جديد إذا لم يكن موجود
-- Create new admin if not exists
-- ===================================

-- حذف الأدمن إذا كان موجوداً
DELETE FROM users WHERE username = 'admin';

-- إنشاء أدمن جديد
-- Password: admin123 (hashed with bcrypt)
INSERT INTO users (username, name, email, password, created_at, updated_at) 
VALUES (
  'admin',
  'مدير النظام',
  '<EMAIL>',
  '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm',
  NOW(),
  NOW()
);

-- ===================================
-- 7. التحقق من إنشاء الأدمن
-- Verify admin creation
-- ===================================

SELECT 'Admin created:' as status, username, name, email FROM users WHERE username = 'admin';

-- ===================================
-- 8. معلومات تسجيل الدخول
-- Login information
-- ===================================

/*
بعد تنفيذ هذا Script:

🔐 بيانات تسجيل الدخول:
- اسم المستخدم: admin
- كلمة المرور: admin123

📊 ما سيحدث:
1. سيتم حذف الأدمن القديم (إن وجد)
2. سيتم إنشاء أدمن جديد بكلمة مرور صحيحة
3. ستتمكن من تسجيل الدخول
4. ستظهر الطلاب الـ 1233

🚀 الخطوات:
1. نفذ هذا SQL في phpMyAdmin
2. افتح التطبيق
3. سجل دخول بـ admin / admin123
4. اذهب لإدارة الطلاب
5. ستظهر الطلاب مع pagination!

⚠️ ملاحظة:
إذا لم يكن هناك حقل username في جدول users، أضفه أولاً:
ALTER TABLE users ADD COLUMN username VARCHAR(255) UNIQUE AFTER id;
*/
