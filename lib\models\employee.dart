class Employee {
  final int id;
  final String fullName;
  final String? phone;
  final String contractType;
  final String employeeType;
  final String jobStatus;
  final String? automaticNumber;
  final String? financialNumber;
  final String? stateCooperativeNumber;
  final String? bankAccountNumber;
  final String username;
  final String? password;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Employee({
    required this.id,
    required this.fullName,
    this.phone,
    required this.contractType,
    required this.employeeType,
    required this.jobStatus,
    this.automaticNumber,
    this.financialNumber,
    this.stateCooperativeNumber,
    this.bankAccountNumber,
    required this.username,
    this.password,
    this.createdAt,
    this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'] ?? 0,
      fullName: json['full_name'] ?? '',
      phone: json['phone'],
      contractType: json['contract_type'] ?? '',
      employeeType: json['employee_type'] ?? '',
      jobStatus: json['job_status'] ?? '',
      automaticNumber: json['automatic_number'],
      financialNumber: json['financial_number'],
      stateCooperativeNumber: json['state_cooperative_number'],
      bankAccountNumber: json['bank_account_number'],
      username: json['username'] ?? '',
      password: json['password'],
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'phone': phone,
      'contract_type': contractType,
      'employee_type': employeeType,
      'job_status': jobStatus,
      'automatic_number': automaticNumber,
      'financial_number': financialNumber,
      'state_cooperative_number': stateCooperativeNumber,
      'bank_account_number': bankAccountNumber,
      'username': username,
      'password': password,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Employee copyWith({
    int? id,
    String? fullName,
    String? phone,
    String? contractType,
    String? employeeType,
    String? jobStatus,
    String? automaticNumber,
    String? financialNumber,
    String? stateCooperativeNumber,
    String? bankAccountNumber,
    String? username,
    String? password,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      phone: phone ?? this.phone,
      contractType: contractType ?? this.contractType,
      employeeType: employeeType ?? this.employeeType,
      jobStatus: jobStatus ?? this.jobStatus,
      automaticNumber: automaticNumber ?? this.automaticNumber,
      financialNumber: financialNumber ?? this.financialNumber,
      stateCooperativeNumber: stateCooperativeNumber ?? this.stateCooperativeNumber,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      username: username ?? this.username,
      password: password ?? this.password,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Employee(id: $id, fullName: $fullName, username: $username, jobStatus: $jobStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Employee && other.id == id && other.username == username;
  }

  @override
  int get hashCode => id.hashCode ^ username.hashCode;

  // Helper methods for UI
  String get displayName => fullName;
  String get statusColor {
    switch (jobStatus) {
      case 'نشط':
        return 'green';
      case 'غير نشط':
        return 'red';
      case 'إجازة':
        return 'orange';
      case 'مفصول':
        return 'grey';
      default:
        return 'blue';
    }
  }

  String get contractTypeDisplay {
    switch (contractType) {
      case 'دوام كامل':
        return 'Full Time';
      case 'دوام جزئي':
        return 'Part Time';
      case 'مؤقت':
        return 'Temporary';
      case 'استشاري':
        return 'Consultant';
      default:
        return contractType;
    }
  }

  String get employeeTypeDisplay {
    switch (employeeType) {
      case 'مدرس':
        return 'Teacher';
      case 'إداري':
        return 'Admin';
      case 'فني':
        return 'Technical';
      case 'عامل':
        return 'Worker';
      case 'مشرف':
        return 'Supervisor';
      default:
        return employeeType;
    }
  }
}
