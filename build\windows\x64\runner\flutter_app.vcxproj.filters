﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\apps\flutter-app\windows\runner\flutter_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\apps\flutter-app\windows\runner\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\apps\flutter-app\windows\runner\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\apps\flutter-app\windows\runner\win32_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\apps\flutter-app\windows\flutter\generated_plugin_registrant.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\apps\flutter-app\windows\runner\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="C:\Users\<USER>\Desktop\apps\flutter-app\windows\runner\Runner.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{FEF21877-E2A7-3167-AF01-AC84A686E0A1}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
